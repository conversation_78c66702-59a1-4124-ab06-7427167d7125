# Stage 1: Build the app
FROM node:22-slim AS builder
WORKDIR /app

# Install dependencies first (for better caching)
COPY package*.json ./
RUN npm install

# Copy the rest of the source code
COPY . .

# Deploy prisma migrations
RUN npm run migrate:deploy

# Generate Prisma client
RUN npm run generate

# Build the NestJS app
RUN npm run build

# Stage 2: Run the app
FROM node:22-alpine
WORKDIR /app

# Copy only what's needed to run
COPY package*.json ./
RUN npm install --only=production

# Copy Prisma schema
COPY prisma ./prisma

# Generate Prisma client inside final container (so it matches prod node_modules)
RUN npx prisma generate

# Copy compiled code from build stage
COPY --from=builder /app/dist ./dist

# Expose application port
EXPOSE 3000

# Start application
CMD ["npm", "run", "start:prod"]