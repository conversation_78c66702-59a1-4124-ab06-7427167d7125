import { EngagespotClient } from '@engagespot/node';
import { Injectable } from '@nestjs/common';

@Injectable()
export class EngagespotService {
  private readonly apiKey: string;
  private readonly apiSecret: string;

  constructor() {
    this.apiKey = process.env.ENGAGESPOT_API_KEY || 'osb3eu3u2jruhela9im70n';
    this.apiSecret =
      process.env.ENGAGESPOT_API_SECRET ||
      'p7pooomk3lto97amp36red1hc1bjeei28je7fa17jbh2e075';
  }

  async send(
    type: string,
    recipients: Array<string>,
    data?: object,
    sendAt?: string,
  ) {
    try {
      const client = await this.engageSpotClient();
      const result = await client.send({
        notification: {
          workflow: { identifier: type },
        },
        data: data,
        sendTo: {
          recipients,
        },
        sendAt,
      });
      console.log(`engagespot notification sent: ${type} `);
      console.log(result);
      return true;
    } catch (err) {
      console.log(`engagespot notification error`);
      console.error(err);
      return false;
    }
  }

  async createOrUpdateUser(
    identifier: string,
    user: { name?: string; email?: string },
  ) {
    try {
      const client = await this.engageSpotClient();
      await client.createOrUpdateUser(identifier, {
        name: user.name,
        email: user.email,
      });
      return true;
    } catch (err) {
      console.log(`engagespot user create error: ${err.message}`);
      return false;
    }
  }

  async engageSpotClient() {
    return EngagespotClient({ apiKey: this.apiKey, apiSecret: this.apiSecret });
  }

  // async getWorkflowIdentifier(type: string) {
  //   let workflowIdentifier: any;

  //   switch (type) {
  //     case 'forgot_password':
  //       workflowIdentifier = process.env.FORGOT_PASSWORD || 'forgot_password';
  //       break;

  //     default:
  //       workflowIdentifier = null;
  //   }
  //   return workflowIdentifier;
  // }
}
