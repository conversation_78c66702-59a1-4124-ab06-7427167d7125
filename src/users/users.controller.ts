import {
  Controller,
  Post,
  Body,
  Patch,
  Request,
  UseGuards,
  Get,
  BadRequestException,
  Param,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { RegisterUserDto } from './dto/register-user.dto';
import { UpdateUserProfileDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { UserRegisterResponseDto } from './dto/user-response.dto';
import { SubscriptionCountsResponseDto } from './dto/subscription-counts.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post('register')
  @ApiOperation({
    summary: 'Register a new user',
    description: 'Creates a new user account with default user role',
  })
  @ApiResponse({
    status: 201,
    description: 'User successfully created',
    type: UserRegisterResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Email already exists',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        message: { type: 'string', example: 'Email already exists' },
        status: { type: 'boolean', example: false },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'array',
          items: { type: 'string' },
          example: ['email must be an email'],
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  async register(@Body() registerUserDto: RegisterUserDto): Promise<any> {
    return await this.usersService.register(registerUserDto);
  }

  @Patch('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
  })
  async updateProfile(
    @Request() req,
    @Body() updateUserProfileDto: UpdateUserProfileDto,
  ) {
    return this.usersService.updateProfile(req.user.id, updateUserProfileDto);
  }

  @Patch(':userid/change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change user password' })
  @ApiResponse({
    status: 200,
    description: 'Password changed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Validation error or passwords do not match',
  })
  @ApiResponse({
    status: 401,
    description: 'Current password is incorrect',
  })
  async changePassword(
    @Request() req,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    return this.usersService.changePassword(req.user.id, changePasswordDto);
  }

  @Get('dashboard')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user subscription counts',
    description:
      'Returns counts of owned subscriptions, active memberships, and pending invites for the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved subscription counts',
    type: SubscriptionCountsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User is not authenticated',
    schema: {
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async getSubscriptionCounts(@Request() req) {
    return this.usersService.getSubscriptionCounts(req.user.id);
  }

  @Post(':userId/resend-verification')
  @ApiOperation({ summary: 'Resend email verification OTP' })
  @ApiResponse({
    status: 200,
    description: 'Verification OTP resent to your email.',
  })
  async resendVerification(@Param('userId') userId: number) {
    // Fetch userId from route param
    return this.usersService.resendVerificationOtp(userId);
  }

  @Post(':userId/verify-email')
  @ApiOperation({ summary: 'Verify email OTP' })
  @ApiResponse({
    status: 200,
    description: 'Email verified successfully, tokens returned.',
  })
  async verifyEmailOtp(
    @Body('otp') otp: string,
    @Param('userId') userId: number,
  ) {
    if (!otp) {
      throw new BadRequestException('Verification otp is missing in request');
    }
    return this.usersService.verifyEmailOtp(Number(userId), otp);
  }
}
