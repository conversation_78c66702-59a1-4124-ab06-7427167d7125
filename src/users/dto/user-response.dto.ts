import { ApiProperty } from '@nestjs/swagger';

export class RoleResponseDto {
  @ApiProperty({
    example: 1,
    description: 'Role identifier',
  })
  id: number;

  @ApiProperty({
    example: 'user',
    description: 'Role name',
  })
  name: string;
}

export class UserRoleResponseDto {
  @ApiProperty({
    example: 1,
    description: 'User role mapping identifier',
  })
  id: number;

  @ApiProperty({
    type: RoleResponseDto,
    description: 'Role information',
  })
  role: RoleResponseDto;
}

export class UserResponseDto {
  @ApiProperty({
    example: 1,
    description: 'User identifier',
  })
  id: number;

  @ApiProperty({
    example: 'John Doe',
    description: "User's full name",
  })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: "User's email address",
  })
  email: string;

  @ApiProperty({
    example: false,
    description: 'KYC verification status',
  })
  is_kyc_verified: boolean;

  @ApiProperty({
    example: 'active',
    description: 'User account status',
  })
  status: string;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'Account creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    type: [UserRoleResponseDto],
    description: "User's assigned roles",
  })
  userRoles: UserRoleResponseDto[];
}
export class TokensDto {
  @ApiProperty({
    description: 'access token',
  })
  access_token: string;

  @ApiProperty({
    description: 'refresh token',
  })
  refresh_token: string;
}
export class UserRegisterResponseDto {
  @ApiProperty({
    type: [UserResponseDto],
    description: 'success message',
    example: 'User registered',
  })
  message: string;
  @ApiProperty({
    type: [UserResponseDto],
    description: "User's data",
  })
  user: UserResponseDto;
  @ApiProperty({
    type: [TokensDto],
    description: 'tokens data',
  })
  tokens: TokensDto;
}
