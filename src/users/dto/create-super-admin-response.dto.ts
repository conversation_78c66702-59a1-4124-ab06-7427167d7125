import { ApiProperty } from '@nestjs/swagger';

export class CreateSuperAdminResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'Super admin created successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    example: {
      email: '<EMAIL>',
      password: 'admin@feemates',
    },
    description: 'Super admin credentials',
  })
  data: {
    email: string;
    password: string;
  };
}

export class SuperAdminExistsResponseDto {
  @ApiProperty({
    example: false,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'Super admin already exists',
    description: 'Message indicating admin already exists',
  })
  message: string;
}
