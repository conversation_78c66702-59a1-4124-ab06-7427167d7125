import { ApiProperty } from '@nestjs/swagger';

export class SubscriptionCountsData {
  @ApiProperty({
    description: 'Number of subscriptions created by the user',
    example: 5,
    type: Number,
  })
  owned_subscriptions: number;

  @ApiProperty({
    description: 'Number of subscriptions where user is an active member',
    example: 3,
    type: Number,
  })
  active_member_subscriptions: number;

  @ApiProperty({
    description: 'Number of pending subscription invites',
    example: 2,
    type: Number,
  })
  invited_subscriptions: number;
}

export class SubscriptionCountsResponseDto {
  @ApiProperty({
    description: 'Response message',
    example: 'Subscription counts fetched successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Subscription counts data',
    type: SubscriptionCountsData,
  })
  data: SubscriptionCountsData;
}
