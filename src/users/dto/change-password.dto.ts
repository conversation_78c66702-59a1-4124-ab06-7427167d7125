import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  MinLength,
  Matches,
  MaxLength,
} from 'class-validator';

export class ChangePasswordDto {
  @ApiProperty({
    description: 'Current password',
    example: 'CurrentPass123!',
  })
  @IsNotEmpty({ message: 'Please enter your current password.' })
  @IsString()
  @MaxLength(160)
  current_password: string;

  @ApiProperty({
    description:
      'New password (min 8 chars, must include uppercase, lowercase, number, symbol)',
    example: 'NewPass123!',
  })
  @IsNotEmpty({ message: 'Please enter a new password.' })
  @IsString()
  @MinLength(8)
  @MaxLength(160)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]+$/,
    {
      message:
        'Password must be at least 8 characters and include uppercase, lowercase, a number, and a symbol.',
    },
  )
  new_password: string;

  @ApiProperty({
    description: 'Confirm new password',
    example: 'NewPass123!',
  })
  @IsNotEmpty({ message: 'Please confirm your new password.' })
  @IsString()
  @MaxLength(160)
  confirm_password: string;
}
