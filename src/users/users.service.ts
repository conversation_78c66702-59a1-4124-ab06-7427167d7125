import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { RegisterUserDto } from './dto/register-user.dto';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcrypt';
import { TokenService } from '../auth/services/jwt.service';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { UpdateUserProfileDto } from './dto/update-user.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import {
  kyc_status,
  SubscriptionStatus,
  subscriptionUserStatus,
  user,
  user_type,
} from '@prisma/client';
import { UserKycService } from '../user_kyc/user_kyc.service';
import { OtpService } from '../common/services/otp.service';
import { WorkflowIdentifiers } from 'src/engagespot/constants/workflow.constants';

@Injectable()
export class UsersService {
  constructor(
    private prisma: PrismaService,
    private tokenService: TokenService,
    private engagespotService: EngagespotService,
    private userKycService: UserKycService,
    private otpService: OtpService, // Inject OtpService
  ) {}

  async register(registerUserDto: RegisterUserDto) {
    const existingUser = await this.prisma.user.findUnique({
      where: { email: registerUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException(
        'Email already registered. Please log in instead.',
      );
    }

    const hashedPassword = await bcrypt.hash(registerUserDto.password, 10);

    let userRole = await this.prisma.role.findFirst({
      where: { name: 'user' },
    });

    if (!userRole) {
      userRole = await this.prisma.role.create({
        data: {
          name: 'user',
        },
      });
    }

    const user = await this.prisma.user.create({
      data: {
        name: registerUserDto.name,
        email: registerUserDto.email,
        password: hashedPassword,
        email_verified_at: null,
        userRoles: {
          create: [{ role_id: userRole.id }],
        },
      },
      include: {
        userRoles: {
          select: {
            id: true,
            role: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // const tokens = this.tokenService.generateTokens({
    //   sub: user.id,
    //   email: user.email,
    //   roles: user.userRoles.map((role) => role.role.name),
    // });

    //Send OTP to user's email
    await this.generateOtpAndSend(user);

    await this.prisma.subscription_user.updateMany({
      where: {
        invited_email: user.email,
      },
      data: {
        user_id: user.id,
      },
    });

    return {
      message:
        'User registered successfully. Please verify your email with the OTP sent.',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        is_kyc_verified: user.is_kyc_verified,
        status: user.status,
        roles: user.userRoles.map((ur) => ur.role.name),
        email_verified_at: user.email_verified_at,
      },
      invited_subscriptions: await this.prisma.subscription_user.count({
        where: {
          user_id: user.id,
          status: subscriptionUserStatus.invited,
        },
      }),
      tokens: null,
      email_verification_required: true,
    };
  }

  async updateProfile(
    userId: number,
    updateUserProfileDto: UpdateUserProfileDto,
  ) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        name: updateUserProfileDto.name,
        avatar: updateUserProfileDto.avatar,
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        is_kyc_verified: true,
        status: true,
      },
    });

    await this.engagespotService.createOrUpdateUser(user.email, {
      name: user.name || undefined,
      email: user.email,
    });

    return {
      message: 'Profile updated successfully',
      user: updatedUser,
    };
  }

  async changePassword(userId: number, changePasswordDto: ChangePasswordDto) {
    if (changePasswordDto.new_password !== changePasswordDto.confirm_password) {
      throw new BadRequestException('Passwords do not match.');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        password: true,
        google_id: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.password) {
      throw new BadRequestException(
        'Password changes aren’t supported for Google-registered accounts. Please use forgot password to reset password.',
      );
    }

    if (!user.password) {
      throw new NotFoundException('User has no password');
    }

    const isCurrentPasswordValid = await bcrypt.compare(
      changePasswordDto.current_password,
      user.password,
    );

    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    const isSamePassword = await bcrypt.compare(
      changePasswordDto.new_password,
      user.password,
    );

    if (isSamePassword) {
      throw new BadRequestException(
        'The new password must be different from the current password.',
      );
    }

    const hashedPassword = await bcrypt.hash(
      changePasswordDto.new_password,
      10,
    );
    await this.prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    return {
      message: 'Your password has been changed successfully.',
    };
  }

  async getSubscriptionCounts(userId: number) {
    const [subscriptionCounts] = await Promise.all([
      this.prisma.$queryRawUnsafe<any[]>(
        `
        SELECT
          SUM(CASE WHEN su.user_type = ? AND su.status = ? THEN 1 ELSE 0 END) AS owned_subscriptions,
          SUM(CASE WHEN su.user_type = ? AND su.status = ? THEN 1 ELSE 0 END) AS active_member_subscriptions,
          SUM(CASE WHEN su.user_type = ? AND su.status = ? THEN 1 ELSE 0 END) AS invited_subscriptions
        FROM subscription_users su
        INNER JOIN subscriptions s ON su.subscription_id = s.id
        WHERE su.user_id = ? AND s.status = ? AND s.deletedAt IS NULL
      `,
        user_type.owner,
        subscriptionUserStatus.active,
        user_type.member,
        subscriptionUserStatus.active,
        user_type.member,
        subscriptionUserStatus.invited,
        userId,
        SubscriptionStatus.active,
      ),
      // this.userKycService.findUserKycByUserId(userId),
    ]);
    const is_kyc_verified =
      await this.userKycService.updateUserKycStatusAndReturnStatus(userId);

    const [counts] = subscriptionCounts;

    return {
      message: 'Subscription counts and KYC details fetched successfully',
      data: {
        owned_subscriptions: Number(counts.owned_subscriptions || 0),
        member_subscriptions: Number(counts.active_member_subscriptions || 0),
        invited_subscriptions: Number(counts.invited_subscriptions || 0),
        is_kyc_verified,
        kyc: null,
      },
    };
  }

  async resendVerificationOtp(userId: number) {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.email_verified_at) {
      throw new BadRequestException('Email already verified');
    }

    await this.generateOtpAndSend(user);

    return { message: 'Verification OTP resent to your email.' };
  }

  async generateOtpAndSend(user: user) {
    // Generate new OTP secret and OTP
    const otpSecret = this.otpService.generateSecret();
    const otp = this.otpService.generateOtp(otpSecret);
    await this.prisma.user.update({
      where: { id: user.id },
      data: { otp_secret: otpSecret },
    });

    await this.engagespotService.createOrUpdateUser(user.email, {
      name: user.name || undefined,
      email: user.email,
    });

    await this.engagespotService.send(
      WorkflowIdentifiers.EMAIL_VERIFICATION,
      [user.email],
      { otp, name: user.name },
    );
    return otp;
  }

  async verifyEmailOtp(userId: number, otp: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: Number(userId) },
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (user.email_verified_at) {
      throw new BadRequestException('Email already verified');
    }
    if (!user.otp_secret) {
      throw new BadRequestException('No OTP found. Please request a new OTP.');
    }
    const result = this.otpService.validateOtp(otp, user.otp_secret);
    if (result == null) {
      throw new UnauthorizedException('Invalid OTP');
    }
    // Mark email as verified and clear otp_secret
    const updatedUser = await this.prisma.user.update({
      where: { id: Number(userId) },
      data: {
        email_verified_at: new Date(),
        otp_secret: null,
      },
      select: {
        id: true,
        name: true,
        email: true,
        is_kyc_verified: true,
        status: true,
        userRoles: {
          select: {
            role: { select: { name: true } },
          },
        },
        email_verified_at: true,
      },
    });
    // Generate tokens
    const tokens = this.tokenService.generateTokens({
      sub: updatedUser.id,
      email: updatedUser.email,
      roles: updatedUser.userRoles.map((ur) => ur.role.name),
    });
    return {
      message: 'Email verified successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        is_kyc_verified: updatedUser.is_kyc_verified,
        status: updatedUser.status,
        roles: updatedUser.userRoles.map((ur) => ur.role.name),
        is_otp_verified: user.email_verified_at ? true : false,
      },
      invited_subscriptions: await this.prisma.subscription_user.count({
        where: {
          user_id: user.id,
          status: subscriptionUserStatus.invited,
        },
      }),
      ...tokens,
    };
  }

  async createSuperAdmin() {
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin@feemates';

    // Check if super admin already exists
    const existingAdmin = await this.prisma.user.findUnique({
      where: { email: adminEmail },
    });

    if (existingAdmin) {
      return {
        status: false,
        message: 'Super admin already exists',
      };
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(adminPassword, 10);

    // Find or create super admin role
    let superAdminRole = await this.prisma.role.findFirst({
      where: { name: 'super_admin' },
    });

    if (!superAdminRole) {
      superAdminRole = await this.prisma.role.create({
        data: {
          name: 'super_admin',
        },
      });
    }

    // Create super admin user
    const superAdmin = await this.prisma.user.create({
      data: {
        name: 'Super Admin',
        email: adminEmail,
        password: hashedPassword,
        email_verified_at: new Date(), // Auto-verify super admin
        userRoles: {
          create: [{ role_id: superAdminRole.id }],
        },
      },
      include: {
        userRoles: {
          select: {
            id: true,
            role: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    return {
      status: true,
      message: 'Super admin created successfully',
      data: {
        email: adminEmail,
        password: adminPassword,
      },
    };
  }
}
