import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { PrismaService } from '../prisma/prisma.service';
import { EngagespotModule } from 'src/engagespot/engagespot.module';
import { UserKycModule } from '../user_kyc/user_kyc.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [AuthModule, EngagespotModule, UserKycModule, CommonModule],
  controllers: [UsersController],
  providers: [UsersService, PrismaService],
  exports: [UsersService],
})
export class UsersModule {}
