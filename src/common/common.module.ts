import { Module } from '@nestjs/common';
import { CommonService } from './common.service';
import { ResponseService } from './services/response.service';
import { EncryptionService } from './services/encryption.service';
import { OtpService } from './services/otp.service';
import { SubscriptionCalculationService } from './services/subscription-calculation.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [
    CommonService,
    ResponseService,
    EncryptionService,
    OtpService,
    SubscriptionCalculationService,
  ],
  exports: [
    ResponseService,
    EncryptionService,
    OtpService,
    SubscriptionCalculationService,
  ],
})
export class CommonModule {}
