import { Module } from '@nestjs/common';
import { CommonService } from './common.service';
import { ResponseService } from './services/response.service';
import { EncryptionService } from './services/encryption.service';
import { OtpService } from './services/otp.service';

@Module({
  providers: [CommonService, ResponseService, EncryptionService, OtpService],
  exports: [ResponseService, EncryptionService, OtpService],
})
export class CommonModule {}
