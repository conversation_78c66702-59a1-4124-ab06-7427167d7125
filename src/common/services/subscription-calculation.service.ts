import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { SubscriptionStatus, subscriptionUserStatus } from '@prisma/client';

export interface SubscriptionCalculationResult {
  subscription_id: number;
  members_count: number;
  owner_share: number;
  total_price: number;
  per_person_price: number;
}

@Injectable()
export class SubscriptionCalculationService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Calculate owner share and member count for a subscription
   * @param subscription_id - The subscription ID to calculate for
   * @returns Promise<SubscriptionCalculationResult> - Calculated values
   */
  async calculateOwnerShareAndMemberCount(
    subscription_id: number,
  ): Promise<SubscriptionCalculationResult> {
    try {
      // Fetch subscription with member count in a single query
      const subscription = await this.prisma.subscription.findFirst({
        where: { id: subscription_id },
        select: {
          id: true,
          price: true,
          status: true,
          per_person_price: true,
          members_count: true,
          owner_share: true,
          _count: {
            select: {
              members: {
                where: {
                  status: {
                    in: [
                      subscriptionUserStatus.active,
                      subscriptionUserStatus.removed,
                    ],
                  },
                },
              },
            },
          },
        },
      });

      if (!subscription) {
        throw new Error(`Subscription with ID ${subscription_id} not found`);
      }

      if (!subscription.price || !subscription.per_person_price) {
        throw new Error(
          `Subscription ${subscription_id} has invalid pricing configuration`,
        );
      }

      const members_count = subscription._count.members;
      const total_price = Number(subscription.price);
      const per_person_price = Number(subscription.per_person_price);

      if (subscription.status === SubscriptionStatus.cancelled) {
        return {
          subscription_id,
          members_count: Number(subscription.members_count) || 0,
          owner_share: Number(subscription.owner_share || 0),
          total_price,
          per_person_price,
        };
      }
      // Calculate owner share
      // If there's only 1 member (owner), owner pays full price
      // If there are multiple members, owner pays: total_price - (per_person_price * other_members)
      const owner_share =
        members_count > 1
          ? total_price - per_person_price * (members_count - 1)
          : total_price;

      return {
        subscription_id,
        members_count,
        owner_share,
        total_price,
        per_person_price,
      };
    } catch (error) {
      console.error(
        `Error calculating owner share for subscription ${subscription_id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Calculate and update owner share and member count in the database
   * @param subscription_id - The subscription ID to update
   * @returns Promise<SubscriptionCalculationResult> - Updated values
   */
  async calculateAndUpdateOwnerShareAndMemberCount(
    subscription_id: number,
  ): Promise<SubscriptionCalculationResult> {
    try {
      return await this.prisma.$transaction(async (tx) => {
        // Calculate the values
        const calculation =
          await this.calculateOwnerShareAndMemberCount(subscription_id);

        // Update the subscription with calculated values
        await tx.subscription.update({
          where: { id: subscription_id },
          data: {
            members_count: calculation.members_count,
            owner_share: calculation.owner_share,
          },
        });

        return calculation;
      });
    } catch (error) {
      console.error(
        `Error updating owner share for subscription ${subscription_id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Batch calculate owner share and member count for multiple subscriptions
   * @param subscription_ids - Array of subscription IDs
   * @returns Promise<SubscriptionCalculationResult[]> - Array of calculated values
   */
  async batchCalculateOwnerShareAndMemberCount(
    subscription_ids: number[],
  ): Promise<SubscriptionCalculationResult[]> {
    try {
      const calculations = await Promise.all(
        subscription_ids.map((id) =>
          this.calculateOwnerShareAndMemberCount(id),
        ),
      );

      return calculations;
    } catch (error) {
      console.error('Error in batch calculation:', error);
      throw error;
    }
  }

  /**
   * Batch calculate and update owner share and member count for multiple subscriptions
   * @param subscription_ids - Array of subscription IDs
   * @returns Promise<SubscriptionCalculationResult[]> - Array of updated values
   */
  async batchCalculateAndUpdateOwnerShareAndMemberCount(
    subscription_ids: number[],
  ): Promise<SubscriptionCalculationResult[]> {
    try {
      return await this.prisma.$transaction(async (tx) => {
        const results: SubscriptionCalculationResult[] = [];

        for (const subscription_id of subscription_ids) {
          const calculation =
            await this.calculateOwnerShareAndMemberCount(subscription_id);

          await tx.subscription.update({
            where: { id: subscription_id },
            data: {
              members_count: calculation.members_count,
              owner_share: calculation.owner_share,
            },
          });

          results.push(calculation);
        }

        return results;
      });
    } catch (error) {
      console.error('Error in batch update:', error);
      throw error;
    }
  }

  /**
   * Validate subscription pricing configuration
   * @param subscription_id - The subscription ID to validate
   * @returns Promise<boolean> - Whether the pricing is valid
   */
  async validateSubscriptionPricing(subscription_id: number): Promise<boolean> {
    try {
      const subscription = await this.prisma.subscription.findFirst({
        where: { id: subscription_id },
        select: {
          price: true,
          per_person_price: true,
          max_no_of_participants: true,
        },
      });

      if (!subscription) {
        return false;
      }

      const total_price = Number(subscription.price);
      const per_person_price = Number(subscription.per_person_price);
      const max_participants = subscription.max_no_of_participants;

      // Validate that pricing makes sense
      if (total_price <= 0 || per_person_price <= 0) {
        return false;
      }

      // Validate that owner share would be positive even at max capacity
      const min_owner_share =
        total_price - per_person_price * (max_participants - 1);

      return min_owner_share > 0;
    } catch (error) {
      console.error(
        `Error validating pricing for subscription ${subscription_id}:`,
        error,
      );
      return false;
    }
  }
}
