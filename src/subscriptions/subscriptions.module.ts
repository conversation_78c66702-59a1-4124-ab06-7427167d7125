import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { SubscriptionsService } from './subscriptions.service';
import { SubscriptionsController } from './subscriptions.controller';
import { CronService } from './services/cron.service';
import { PrismaModule } from '../prisma/prisma.module';
import { CommonModule } from 'src/common/common.module';
import { EngagespotModule } from 'src/engagespot/engagespot.module';
import { FileUploadModule } from 'src/file-upload/file-upload.module';
import { StripeModule } from 'src/stripe/stripe.module';

@Module({
  imports: [
    PrismaModule,
    CommonModule,
    EngagespotModule,
    FileUploadModule,
    StripeModule,
  ],
  controllers: [SubscriptionsController],
  providers: [SubscriptionsService, CronService],
  exports: [SubscriptionsService],
})
export class SubscriptionsModule {}
