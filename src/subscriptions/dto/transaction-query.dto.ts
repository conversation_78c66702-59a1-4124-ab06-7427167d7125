import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { payment_status } from '@prisma/client';

export class TransactionQueryDto {
  @ApiProperty({
    required: false,
    description: 'Page number for pagination',
    default: 1,
  })
  @IsOptional()
  page?: number;

  @ApiProperty({
    required: false,
    description: 'Number of items per page',
    default: 10,
  })
  @IsOptional()
  limit?: number;

  @ApiProperty({
    required: false,
    enum: payment_status,
    description: 'Filter transactions by payment status',
  })
  @IsEnum(payment_status)
  @IsOptional()
  status?: payment_status;
}
