import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, IsIn } from 'class-validator';

export class SubscriptionQueryDto {
  @ApiPropertyOptional({ type: Number, default: 1 })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({ type: Number, default: 10 })
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({
    type: String,
    description: 'Search by subscription name',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    type: String,
    enum: ['owner', 'member', 'all'],
    description: 'Filter by subscription type: owner, member, or all',
    default: 'all',
    required: false,
  })
  @IsOptional()
  @IsIn(['owner', 'member', 'all'])
  type?: 'owner' | 'member' | 'all' = 'all';

  @ApiPropertyOptional({
    type: String,
    enum: ['active', 'disabled', 'expired'],
    description: 'Filter by subscription status',
  })
  @IsOptional()
  @IsIn(['active', 'disabled', 'expired'])
  status?: 'active' | 'disabled' | 'expired';

  @ApiPropertyOptional({
    type: String,
    enum: ['name', 'createdAt', 'per_person_price'],
    description: 'Sort by field',
    default: 'createdAt',
  })
  @IsOptional()
  @IsIn(['name', 'createdAt', 'owner_share', 'per_person_price'])
  sortBy?: 'name' | 'createdAt' | 'owner_share' | 'per_person_price' =
    'createdAt';

  @ApiPropertyOptional({
    type: String,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    default: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}
