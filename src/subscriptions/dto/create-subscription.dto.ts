import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsNumber,
  IsDate,
  IsEnum,
  IsDecimal,
  IsNotEmpty,
  MaxLength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SubscriptionStatus } from '@prisma/client';

export class CreateSubscriptionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(160)
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  @MaxLength(160)
  description?: string;

  @ApiProperty({ enum: SubscriptionStatus, default: SubscriptionStatus.active })
  @IsEnum(SubscriptionStatus)
  @IsOptional()
  status?: SubscriptionStatus;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsNotEmpty()
  price: number;

  @ApiProperty({ required: false })
  @IsDecimal()
  @IsOptional()
  per_person_price?: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  max_no_of_participants: number;

  @ApiProperty()
  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @ApiProperty()
  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  @MaxLength(160)
  thumbnail?: string;
}
