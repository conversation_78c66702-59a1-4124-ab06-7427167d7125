import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Request,
  Put,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { SubscriptionsService } from './subscriptions.service';
import { CronService } from './services/cron.service';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { UpdateSubscriptionDto } from './dto/update-subscription.dto';
import { DeleteSubscriptionDto } from './dto/delete-subscription.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SubscriptionQueryDto } from './dto/subscription-query.dto';
import { PortalSessionDto } from './dto/portal-session.dto';
import { TransactionQueryDto } from './dto/transaction-query.dto';

@ApiTags('subscriptions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('subscriptions')
export class SubscriptionsController {
  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly cronService: CronService,
  ) {}

  @Get()
  findAll(@Query() query: SubscriptionQueryDto, @Request() req) {
    return this.subscriptionsService.findAll(req.user.id, query);
  }

  @Get('cron/expired')
  @ApiOperation({ summary: 'Check and update expired subscriptions' })
  @HttpCode(HttpStatus.OK)
  async handleExpiredSubscriptions() {
    return this.cronService.handleExpiredSubscriptions();
  }

  @Get('my-subscriptions')
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  findUserSubscriptions(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.subscriptionsService.findUserSubscriptions(
      req.user.id,
      Number(page) || 1,
      Number(limit) || 10,
    );
  }

  @Get('update-details')
  async updateSubscriptionDetails() {
    return this.subscriptionsService.updateAllSubscriptionDetails();
  }

  @Get('update-next-date')
  async updateNextDate() {
    return this.subscriptionsService.updateNextDate();
  }

  @Get(':id')
  findOne(@Request() req, @Param('id') id: string) {
    return this.subscriptionsService.findOne(req.user.id, +id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
    @Request() req,
  ) {
    return this.subscriptionsService.update(
      +id,
      updateSubscriptionDto,
      req.user.id,
    );
  }

  @Get(':id/members')
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  findSubscriptionMembers(
    @Param('id') id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.subscriptionsService.findSubscriptionMembers(
      +id,
      Number(page) || 1,
      Number(limit) || 10,
    );
  }

  @Get(':id/stats')
  findSubscriptionStats(@Param('id') id: string) {
    return this.subscriptionsService.findSubscriptionStats(+id);
  }

  @Get(':id/transactions')
  @ApiOperation({
    summary: 'Get subscription transactions with optional filters',
  })
  async findSubscriptionTransactions(
    @Param('id') id: string,
    @Request() req,
    @Query() query: TransactionQueryDto,
  ) {
    return this.subscriptionsService.findSubscriptionTransactions(
      req.user.id,
      +id,
      query,
    );
  }

  @Post()
  create(@Body() createSubscriptionDto: CreateSubscriptionDto, @Request() req) {
    return this.subscriptionsService.create(createSubscriptionDto, req.user.id);
  }

  @Post('portal-session')
  async createPortalSession(
    @Body() portalSessionDto: PortalSessionDto,
    @Request() req,
  ) {
    return this.subscriptionsService.createPortalSession(
      req.user.id,
      portalSessionDto.return_url,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a subscription bundle (owner only)' })
  remove(
    @Param('id') id: string,
    @Request() req,
    @Body() deleteSubscriptionDto: DeleteSubscriptionDto,
  ) {
    return this.subscriptionsService.remove(
      +id,
      req.user.id,
      deleteSubscriptionDto,
    );
  }
  @Patch(':subscriptionId/members/:memberId/remove')
  @ApiOperation({ summary: 'Remove a member from subscription (Owner only)' })
  async removeMember(
    @Param('subscriptionId') subscriptionId: string,
    @Param('memberId') memberId: string,
    @Request() req,
  ) {
    return this.subscriptionsService.removeMember(
      +subscriptionId,
      +memberId,
      req.user.id,
    );
  }

  @Patch(':subscriptionId/members/:memberId/leave')
  @ApiOperation({ summary: 'Leave a subscription (Member only)' })
  async leaveSubscription(
    @Param('subscriptionId') subscriptionId: string,
    @Param('memberId') memberId: string,
    @Request() req,
  ) {
    return this.subscriptionsService.leaveSubscription(
      +subscriptionId,
      +memberId,
      req.user.id,
    );
  }
}
