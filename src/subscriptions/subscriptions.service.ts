import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { UpdateSubscriptionDto } from './dto/update-subscription.dto';
import { ResponseService } from 'src/common/services/response.service';
import { SubscriptionQueryDto } from './dto/subscription-query.dto';
import { TransactionQueryDto } from './dto/transaction-query.dto';
import { DeleteSubscriptionDto } from './dto/delete-subscription.dto';
import {
  payment_status,
  stripe_subscription_status,
  SubscriptionStatus,
  subscriptionUserStatus,
  user_type,
} from '@prisma/client';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { StripeService } from '../stripe/stripe.service';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { WorkflowIdentifiers } from 'src/engagespot/constants/workflow.constants';
import { SubscriptionUser } from 'src/subscription_users/entities/subscription_user.entity';
import { SubscriptionCalculationService } from 'src/common/services/subscription-calculation.service';

@Injectable()
export class SubscriptionsService {
  constructor(
    private prisma: PrismaService,
    private responseService: ResponseService,
    private fileUploadService: FileUploadService,
    private stripeService: StripeService,
    private engagespotService: EngagespotService,
    private subscriptionCalculationService: SubscriptionCalculationService,
  ) {}

  async updateAllSubscriptionDetails() {
    try {
      // Get all active subscriptions
      const subscriptions = await this.prisma.subscription.findMany({
        where: {
          deletedAt: null,
        },
        select: {
          id: true,
        },
      });

      // Extract subscription IDs
      const subscriptionIds = subscriptions.map((sub) => sub.id);

      // Use the calculation service to batch update all subscriptions
      await this.subscriptionCalculationService.batchCalculateAndUpdateOwnerShareAndMemberCount(
        subscriptionIds,
      );

      return this.responseService.successResponse(
        'Successfully updated all subscription details',
        { count: subscriptions.length },
      );
    } catch (error) {
      throw new Error(
        `Failed to update subscription details: ${error.message}`,
      );
    }
  }

  async findUserById(id: number) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        stripe_customer_id: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findAll(userId: number, query: SubscriptionQueryDto) {
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;
    const skip = (page - 1) * limit;

    let where: any = {
      deletedAt: null,
    };

    // Add status filter if provided
    if (query.status) {
      where.status = query.status;
    }

    // Add type filter
    if (query.type == 'owner') {
      where = {
        ...where,
        created_by: Number(userId),
      };
    } else if (query.type == 'member') {
      where = {
        ...where,
        members: {
          some: {
            user_id: userId,
            OR: [
              {
                status: subscriptionUserStatus.active,
                user_type: user_type.member,
              },
              {
                status: subscriptionUserStatus.expired,
                user_type: user_type.member,
                joined_at: {
                  not: null,
                },
              },
              {
                status: subscriptionUserStatus.cancelled,
                user_type: user_type.member,
                joined_at: {
                  not: null,
                },
              },
              {
                status: subscriptionUserStatus.removed,
                user_type: user_type.member,
                joined_at: {
                  not: null,
                },
              },
            ],
          },
        },
      };
    } else {
      where = {
        ...where,
        OR: [
          { created_by: userId },
          {
            members: {
              some: {
                user_id: userId,
                user_type: user_type.member,
                status: subscriptionUserStatus.expired,
                joined_at: {
                  not: null,
                },
              },
            },
          },
          {
            members: {
              some: {
                user_id: userId,
                user_type: user_type.member,
                status: subscriptionUserStatus.active,
              },
            },
          },
          {
            members: {
              some: {
                user_id: userId,
                status: subscriptionUserStatus.removed,
                user_type: user_type.member,
              },
            },
          },
        ],
      };
    }

    // Add name search filter
    if (query.name) {
      where = {
        ...where,
        name: {
          contains: query.name,
        },
      };
    }

    // Build orderBy object based on sort parameters
    const orderBy: any = {};
    const sortField = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'desc';
    orderBy[sortField] = sortOrder;

    const [subscriptions, total] = await Promise.all([
      this.prisma.subscription.findMany({
        skip,
        take: limit,
        where,
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          members: {
            where: {
              user_id: userId,
            },
            take: 1,
            select: {
              id: true,
              status: true,
              user_type: true,
              end_date: true,
            },
          },
        },
        orderBy,
      }),
      this.prisma.subscription.count({
        where,
      }),
    ]);

    // Add is_owner property to each subscription
    const subscriptionsWithOwner = await Promise.all(
      subscriptions.map(async (sub) => ({
        ...sub,
        is_owner: sub.created_by === userId,
        thumbnail: sub.thumbnail
          ? await this.fileUploadService.getSignedUrl(sub.thumbnail)
          : sub.thumbnail,
        member: sub.members.length > 0 ? sub.members[0] : null,
        members: undefined,
      })),
    );

    return {
      data: subscriptionsWithOwner,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async create(createSubscriptionDto: CreateSubscriptionDto, userId: number) {
    const duplicateSubscription = await this.prisma.subscription.findFirst({
      where: {
        created_by: userId,
        name: createSubscriptionDto.name,
        price: createSubscriptionDto.price,
        max_no_of_participants: createSubscriptionDto.max_no_of_participants,
      },
    });

    if (duplicateSubscription) {
      throw new ConflictException('Subscription Duplicate Entry');
    }
    // Create Stripe product and price first
    if (
      createSubscriptionDto.max_no_of_participants > createSubscriptionDto.price
    ) {
      throw new ConflictException(
        'Number of participants cannot be greater than the subscription price',
      );
    }
    const user = await this.prisma.user.findUnique({
      where: {
        id: userId,
      },
    });

    if (!user || !user.stripe_connect_account_id || !user.is_kyc_verified) {
      throw new ConflictException(
        'Only KYC-approved users can create subscriptions.',
      );
    }

    const now = new Date();
    now.setHours(0, 0, 0, 0); // Set to start of today

    if (createSubscriptionDto.startDate < now) {
      throw new ConflictException('Start date cannot be less than today.');
    }

    if (createSubscriptionDto.startDate >= createSubscriptionDto.endDate) {
      throw new ConflictException('Start date must be before end date.');
    }

    const startDate = new Date(createSubscriptionDto.startDate);
    const minEndDate = new Date(startDate);
    minEndDate.setDate(minEndDate.getDate() + 30);

    if (new Date(createSubscriptionDto.endDate) < minEndDate) {
      throw new ConflictException(
        'End date must be at least 30 days after the start date.',
      );
    }

    const per_person_price =
      Number(createSubscriptionDto.price) /
      Number(createSubscriptionDto.max_no_of_participants);

    // Create subscription
    // Create Stripe product
    const stripeProduct = await this.stripeService.createProduct(
      createSubscriptionDto.name,
      createSubscriptionDto.description,
    );

    // Create Stripe price
    const stripePrice = await this.stripeService.createPrice(
      stripeProduct.id,
      per_person_price,
    );

    // Create subscription with Stripe IDs
    const subscription = await this.prisma.subscription.create({
      data: {
        ...createSubscriptionDto,
        created_by: userId,
        per_person_price,
        members_count: 1,
        owner_share: createSubscriptionDto.price,
        stripe_plan_id: stripeProduct.id,
        stripe_plan__price_id: stripePrice.id,
      },
      include: {
        owner: true,
      },
    });

    // Add owner as a member
    await this.prisma.subscription_user.create({
      data: {
        subscription_id: subscription.id,
        user_id: subscription.created_by,
        price: Number(subscription.owner_share),
        user_type: user_type.owner,
        invited_email: subscription.owner.email,
        status: subscriptionUserStatus.active,
        joined_at: new Date(),
        stripe_subscription_status: stripe_subscription_status.active,
        start_date: new Date(),
      },
    });

    return this.responseService.successResponse(
      'Subscription created successfully!',
      subscription,
    );
  }

  async findOne(userId: number, id: number) {
    //for subscription calculation if any error occurs double check
    await this.subscriptionCalculationService.calculateAndUpdateOwnerShareAndMemberCount(
      id,
    );

    const subscription = await this.prisma.subscription.findFirst({
      where: {
        id,
        deletedAt: null,
      },
      include: {
        members: {
          select: {
            id: true,
            status: true,
            next_due_date: true,
            start_date: true,
            user_id: true,
          },
          where: {
            user_id: userId,
          },
        },
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }
    subscription['is_owner'] = subscription.created_by == userId ? true : false;
    subscription['member'] =
      subscription.members.length > 0 ? subscription.members[0] : null;
    (subscription as any).members && delete (subscription as any).members;
    subscription['thumbnail'] = subscription.thumbnail
      ? await this.fileUploadService.getSignedUrl(subscription.thumbnail)
      : subscription.thumbnail;
    return this.responseService.successResponse(
      'Subscription fetched successfully.',
      subscription,
    );
  }

  async findOneById(id: number) {
    const subscription = await this.prisma.subscription.findFirst({
      where: {
        id,
        deletedAt: null,
      },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return subscription;
  }

  async update(
    id: number,
    updateSubscriptionDto: UpdateSubscriptionDto,
    userId: number,
  ) {
    const subscription = await this.findOneById(id);
    if (subscription.created_by != userId) {
      throw new ConflictException('Only owner can edit subscription');
    }

    const now = new Date();
    now.setHours(0, 0, 0, 0); // Set to start of today

    if (
      updateSubscriptionDto.endDate &&
      subscription.status != subscriptionUserStatus.active
    ) {
      throw new ConflictException(
        'Cannot update end date unless the subscription is active.',
      );
    }

    if (updateSubscriptionDto.endDate && updateSubscriptionDto.endDate < now) {
      throw new ConflictException('End date cannot be less than today.');
    }

    const startDate = new Date(subscription.startDate);
    const minEndDate = new Date(startDate);
    minEndDate.setDate(minEndDate.getDate() + 30);

    if (
      updateSubscriptionDto.endDate &&
      new Date(updateSubscriptionDto.endDate) < minEndDate
    ) {
      throw new ConflictException(
        'End date must be at least 30 days after the start date.',
      );
    }
    // Update subscription in database
    await this.prisma.subscription.update({
      where: { id },
      data: updateSubscriptionDto,
    });
    const existingSubUsers = await this.prisma.subscription_user.findMany({
      where: {
        subscription_id: subscription.id,
        status: subscriptionUserStatus.active,
        user_type: user_type.member,
      },
      select: {
        invited_email: true,
      },
    });

    // Compare end dates as date values (ignoring time)
    const oldEndDate = subscription.endDate
      ? new Date(subscription.endDate).setHours(0, 0, 0, 0)
      : null;
    const newEndDate = updateSubscriptionDto.endDate
      ? new Date(updateSubscriptionDto.endDate).setHours(0, 0, 0, 0)
      : null;

    if (
      subscription.name != updateSubscriptionDto.name ||
      subscription.description != updateSubscriptionDto.description ||
      oldEndDate !== newEndDate
    ) {
      const existingEmails = existingSubUsers
        .map((user) => user.invited_email)
        .filter((email): email is string => !!email);

      if (existingEmails.length > 0) {
        if (oldEndDate !== newEndDate) {
          await this.engagespotService.send(
            WorkflowIdentifiers.SUBSCRIPTION_UPDATE_END_DATE,
            existingEmails,
            {
              subscription_name: subscription?.name,
              owner_name: subscription.owner?.name,
              end_date: updateSubscriptionDto.endDate,
            },
          );
        } else {
          await this.engagespotService.send(
            WorkflowIdentifiers.SUBSCRIPTION_EDITED,
            existingEmails,
            {
              subscription_name: subscription?.name,
              owner_name: subscription.owner?.name,
            },
          );
        }
      }
    }

    if (subscription.stripe_plan_id) {
      // Update the Stripe product
      await this.stripeService.updateProduct(
        subscription.stripe_plan_id,
        updateSubscriptionDto.name || subscription.name,
        updateSubscriptionDto.description
          ? updateSubscriptionDto.description
          : subscription.description || undefined,
      );
    }

    return this.responseService.successResponse(
      'Subscription updated successfully.',
    );
  }

  async remove(id: number, userId: number, deleteDto: DeleteSubscriptionDto) {
    const subscription = await this.findOneById(id);

    // Ensure only owner can delete
    if (subscription.created_by !== userId) {
      throw new ConflictException('Only owner can delete subscription');
    }

    // Confirm bundle name
    if (subscription.name !== deleteDto.bundle_name) {
      throw new ConflictException(
        'Bundle name does not match for confirmation',
      );
    }

    const subscriptionId = id;

    return await this.prisma.$transaction(async (tx) => {
      if (
        subscription.members_count <= 1 ||
        subscription.status == SubscriptionStatus.expired
      ) {
        // Only owner exists, hard delete everything
        await tx.subscription_user.deleteMany({
          where: { subscription_id: subscriptionId },
        });

        await tx.subscription.delete({
          where: { id: subscriptionId },
        });

        return this.responseService.successResponse(
          'Bundle deleted successfully',
        );
      }

      // Active members case
      const participants = await tx.subscription_user.findMany({
        where: {
          subscription_id: subscriptionId,
          status: subscriptionUserStatus.active,
          user_type: user_type.member,
        },
        include: {
          user: { select: { email: true } },
        },
      });
      const participantIds = participants.map((p) => p.id);

      // Cancel each Stripe subscription
      await Promise.all(
        participants
          .filter((member) => !!member.stripe_subscription_id)
          .map((member) =>
            this.stripeService.cancelSubscription(
              member.stripe_subscription_id!,
            ),
          ),
      );

      // Collect emails for notification
      const participantEmails = participants
        .map((p) => p.user?.email || p.invited_email)
        .filter((email): email is string => !!email);

      if (
        participantEmails.length > 0 &&
        subscription.status == SubscriptionStatus.active
      ) {
        await this.engagespotService.send(
          WorkflowIdentifiers.SUBSCRIPTION_DELETED,
          participantEmails,
          {
            subscription_name: subscription.name,
            owner_name: subscription.owner.name,
          },
        );
      }
      // Remove pending invites
      await tx.subscription_user.deleteMany({
        where: {
          subscription_id: subscriptionId,
          user_type: user_type.member,
          id: {
            notIn: participantIds,
          },
        },
      });

      await tx.subscription_user.updateMany({
        where: {
          subscription_id: subscriptionId,
        },
        data: {
          status: subscriptionUserStatus.cancelled,
        },
      });
      // Mark subscription as cancelled (soft delete)
      await tx.subscription.update({
        where: { id: subscriptionId },
        data: {
          status: SubscriptionStatus.cancelled,
          members_count: participants.length + 1,
        },
      });

      return this.responseService.successResponse(
        'Bundle removed successfully',
      );
    });
  }

  async findUserSubscriptions(userId: number, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    const [subscriptions, total] = await Promise.all([
      this.prisma.subscription.findMany({
        skip,
        take: limit,
        where: {
          created_by: userId,
          deletedAt: null,
        },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.subscription.count({
        where: {
          created_by: userId,
          deletedAt: null,
        },
      }),
    ]);

    return {
      data: subscriptions,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findSubscriptionMembers(subscriptionId: number, page = 1, limit = 10) {
    // Check if subscription exists
    await this.findOneById(subscriptionId);

    const skip = (page - 1) * limit;
    const [rawMembers, total] = await Promise.all([
      this.prisma.subscription_user.findMany({
        skip,
        take: limit,
        where: {
          subscription_id: subscriptionId,
          status: { not: subscriptionUserStatus.pending },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
        orderBy: [{ status: 'desc' }],
      }),
      this.prisma.subscription_user.count({
        where: {
          subscription_id: subscriptionId,
          status: { not: subscriptionUserStatus.pending },
        },
      }),
    ]);

    // Get signed URLs for avatars
    const members = await Promise.all(
      rawMembers.map(async (member) => {
        if (member.user?.avatar) {
          try {
            const avatarUrl = await this.fileUploadService.getSignedUrl(
              member.user.avatar,
            );
            return {
              ...member,
              user: {
                ...member.user,
                avatar: avatarUrl,
              },
            };
          } catch (error) {
            console.error('Error getting avatar signed URL:', error);
          }
        }
        return member;
      }),
    );

    return {
      data: members,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findSubscriptionTransactions(
    userId: number,
    subscriptionId: number,
    query: TransactionQueryDto,
  ) {
    // Check if subscription exists and get ownership info
    const subscription = await this.findOneById(subscriptionId);
    const isOwner = subscription.created_by === userId;

    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build where clause based on user role
    const where: any = {
      subscription_id: subscriptionId,
      deletedAt: null,
    };

    // Add status filter if provided
    if (query.status) {
      where.status = query.status;
    } else {
      where.status = payment_status.completed; // Default filter
    }

    // If not owner, only show user's own transactions
    if (!isOwner) {
      where.user_id = userId;
    }

    const [transactions, total] = await Promise.all([
      this.prisma.transactions.findMany({
        skip,
        take: limit,
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.transactions.count({ where }),
    ]);

    return {
      data: transactions,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async createPortalSession(userId: number, returnUrl: string) {
    const user = await this.findUserById(userId);

    if (!user.stripe_customer_id) {
      throw new Error('User does not have a Stripe customer ID');
    }

    const portalSession = await this.stripeService.createPortalSession(
      user.stripe_customer_id,
      returnUrl,
    );

    return this.responseService.successResponse(
      'Portal session created successfully',
      {
        return_url: portalSession.return_url,
        url: portalSession.url,
      },
    );
  }

  async updateNextDate() {
    const findUserSubscriptions = await this.prisma.subscription_user.findMany({
      where: {
        status: subscriptionUserStatus.active,
        user_type: user_type.member,
      },
    });

    for (const element of findUserSubscriptions) {
      if (!element.start_date) continue; // skip if start_date is null/undefined
      const nextMonth = new Date(element.start_date);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      await this.prisma.subscription_user.update({
        where: {
          id: element.id,
        },
        data: {
          next_due_date: nextMonth,
        },
      });
    }
  }

  async leaveSubscription(
    subscriptionId: number,
    memberId: number,
    userId: number,
  ) {
    // Check if subscription exists
    const subscription = await this.findOneById(subscriptionId);

    // Find subscription user
    const subscriptionUser = await this.prisma.subscription_user.findFirst({
      where: {
        subscription_id: subscriptionId,
        id: memberId,
        user_type: user_type.member,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!subscriptionUser) {
      throw new NotFoundException('Member not found in subscription');
    }

    if (userId !== subscriptionUser?.user_id) {
      throw new ConflictException(
        'You can only remove yourself from subscription',
      );
    }
    if (subscriptionUser.status !== subscriptionUserStatus.active) {
      throw new BadRequestException(
        'You can only leave a subscription if you are an active member.',
      );
    }
    // Cancel Stripe subscription if exists

    if (subscriptionUser.stripe_subscription_id) {
      try {
        await this.stripeService.cancelSubscription(
          subscriptionUser.stripe_subscription_id,
        );
      } catch (err) {
        console.log(`stripe subscription remove Error: ${err.message}`);
      }
    }
    await this.prisma.transactions.deleteMany({
      where: { subscription_user_id: subscriptionUser.id },
    });

    await this.prisma.subscription_user.delete({
      where: { id: subscriptionUser.id },
    });

    // Recalculate members_count and owner_share using the calculation service
    const calculationResult =
      await this.subscriptionCalculationService.calculateAndUpdateOwnerShareAndMemberCount(
        subscriptionId,
      );

    const notificationData = {
      subscription_name: subscription.name,
      owner_name: subscription.owner.name,
      member_name: subscriptionUser.user?.name,
      owner_share: calculationResult.owner_share,
    };
    const email = subscription.owner.email;
    if (email) {
      await this.engagespotService.send(
        WorkflowIdentifiers.LEAVE_FROM_SUBSCRIPTION,
        [email],
        notificationData,
      );
    }
    return this.responseService.successResponse(
      'You have left the subscription',
    );
  }

  async removeMember(subscriptionId: number, memberId: number, userId: number) {
    // Check if subscription exists
    const subscription = await this.findOneById(subscriptionId);

    // Verify authorization
    if (subscription.created_by !== userId) {
      throw new ConflictException('Only owner can remove members');
    }

    // Find subscription user
    const subscriptionUser = await this.prisma.subscription_user.findFirst({
      where: {
        subscription_id: subscriptionId,
        id: memberId,
        user_type: user_type.member,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!subscriptionUser) {
      throw new NotFoundException('Member not found in subscription');
    }

    if (subscriptionUser.status !== subscriptionUserStatus.active) {
      throw new BadRequestException('Only active members can be removed');
    }

    // Cancel Stripe subscription if exists
    if (subscriptionUser.stripe_subscription_id) {
      try {
        await this.stripeService.cancelSubscription(
          subscriptionUser.stripe_subscription_id,
        );
      } catch (err) {
        console.log(`stripe subscription remove Error: ${err.message}`);
      }
    }

    // await this.prisma.transactions.deleteMany({
    //   where: { subscription_user_id: subscriptionUser.id },
    // });

    await this.prisma.subscription_user.update({
      where: { id: subscriptionUser.id },
      data: {
        status: subscriptionUserStatus.removed,
        removed_at: new Date(),
      },
    });

    // Recalculate members_count and owner_share using the calculation service
    const calculationResult =
      await this.subscriptionCalculationService.calculateAndUpdateOwnerShareAndMemberCount(
        subscriptionId,
      );

    const notificationData = {
      subscription_name: subscription.name,
      owner_name: subscription.owner.name,
      member_name: subscriptionUser.user?.name,
      owner_share: calculationResult.owner_share,
    };

    const email = subscriptionUser.user?.email;
    if (email) {
      await this.engagespotService.send(
        WorkflowIdentifiers.REMOVE_FROM_SUBSCRIPTION,
        [email],
        notificationData,
      );
    }
    if (subscription?.owner?.email) {
      await this.engagespotService.send(
        WorkflowIdentifiers.REMOVE_FROM_SUBSCRIPTION_TO_OWNER,
        [subscription?.owner?.email],
        notificationData,
      );
    }

    return this.responseService.successResponse(
      'Participant removed successfully',
    );
  }

  async findSubscriptionStats(subscription_id: number) {
    // Get the first and last day of the current month
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      0,
      23,
      59,
      59,
      999,
    );

    // Get subscription price
    const subscription = await this.prisma.subscription.findUnique({
      where: { id: subscription_id },
      select: { price: true },
    });
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    // Sum of completed transactions for this subscription in the current month
    const result = await this.prisma.transactions.aggregate({
      _sum: { amount: true },
      where: {
        subscription_id,
        status: payment_status.completed,
        createdAt: {
          gte: firstDay,
          lte: lastDay,
        },
      },
    });

    const savings = Number(result._sum.amount) || 0;
    const total_paid =
      Number(subscription.price) > savings
        ? Number(subscription.price) - savings
        : Number(subscription.price);

    return {
      firstDay,
      lastDay,
      total_paid,
      savings,
    };
  }
}
