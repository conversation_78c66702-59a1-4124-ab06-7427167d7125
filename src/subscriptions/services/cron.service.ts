import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as crypto from 'crypto';
import { PrismaService } from '../../prisma/prisma.service';
import { StripeService } from '../../stripe/stripe.service';
import { EngagespotService } from '../../engagespot/engagespot.service';
import { WorkflowIdentifiers } from '../../engagespot/constants/workflow.constants';
import {
  subscriptionUserStatus,
  SubscriptionStatus,
  user_type,
} from '@prisma/client';
import { SubscriptionUser } from 'src/subscription_users/entities/subscription_user.entity';
import { SubscriptionCalculationService } from 'src/common/services/subscription-calculation.service';

@Injectable()
export class CronService {
  constructor(
    private prisma: PrismaService,
    private stripeService: StripeService,
    private engagespotService: EngagespotService,
    private subscriptionCalculationService: SubscriptionCalculationService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleExpiredSubscriptions() {
    console.log('cron running -', new Date());
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    console.log(today);
    // Find subscriptions that have expired
    const expiredSubscriptions = await this.prisma.subscription.findMany({
      where: {
        endDate: {
          lt: today,
        },
        status: 'active',
        deletedAt: null,
      },
      include: {
        members: {
          include: {
            user: {
              select: {
                email: true,
              },
            },
            subscription: true,
          },
        },
      },
    });

    for (const subscription of expiredSubscriptions) {
      // Update subscription status to expired
      await this.prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          status: SubscriptionStatus.expired,
        },
      });

      // Cancel all active member subscriptions in Stripe and update their status
      for (const member of subscription.members) {
        if (
          member.stripe_subscription_id &&
          member.status == subscriptionUserStatus.active
        ) {
          try {
            // Cancel subscription in Stripe
            await this.stripeService.cancelSubscription(
              member.stripe_subscription_id,
            );
          } catch (error) {
            console.error(
              `Failed to cancel Stripe subscription for member ${member.id}:`,
              error,
            );
          }
        }

        // Update member status to expired
        await this.prisma.subscription_user.update({
          where: { id: member.id },
          data: {
            status: subscriptionUserStatus.expired,
            stripe_subscription_status: 'cancelled',
            cancelled_date: new Date(),
          },
        });

        // Send expiration notification to active members
        if (
          member.status === subscriptionUserStatus.active &&
          member.user?.email
        ) {
          this.engagespotService.send(
            WorkflowIdentifiers.SUBSCRIPTION_EXPIRED,
            [member.user.email],
            {
              subscription_name: subscription.name,
              end_date: subscription.endDate.toLocaleDateString(),
            },
          );
        }
      }
    }

    return {
      processed: expiredSubscriptions.length,
      today,
    };
  }
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async sendPaymentReminders() {
    console.log('Running payment reminder cron -', new Date());

    // Calculate the date 3 days from now
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

    // Set start of day (00:00:00)
    const startOfDay = new Date(threeDaysFromNow);
    startOfDay.setHours(0, 0, 0, 0);

    // Set end of day (23:59:59)
    const endOfDay = new Date(threeDaysFromNow);
    endOfDay.setHours(23, 59, 59, 999);

    // Find active subscription users who need payment reminders
    const usersNeedingReminders = await this.prisma.subscription_user.findMany({
      where: {
        status: subscriptionUserStatus.active,
        user_type: user_type.member,
        next_due_date: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        user: true,
        subscription: true,
      },
    });

    // Send reminders to each user
    for (const subscriptionUser of usersNeedingReminders) {
      if (subscriptionUser.user?.email) {
        await this.engagespotService.send(
          WorkflowIdentifiers.PAYMENT_REMINDER,
          [subscriptionUser.user.email],
          {
            subscription_name: subscriptionUser.subscription.name,
            payment_date: threeDaysFromNow.toLocaleDateString(),
          },
        );
      }
    }

    return {
      processed: usersNeedingReminders.length,
      date: threeDaysFromNow,
    };
  }

  @Cron(CronExpression.EVERY_2_HOURS)
  async handleRemovedSubscriptions() {
    console.log('cron running -handleRemovedSubscriptions -- ', new Date());
    const today = new Date();
    // today.setHours(0, 0, 0, 0);
    console.log(today);

    const subscriptionIdsWithRemovedMembers =
      await this.prisma.subscription_user.findMany({
        where: {
          status: subscriptionUserStatus.removed,
          next_due_date: {
            lt: today,
          },
        },
        select: {
          subscription_id: true,
        },
      });

    const subscriptionIds = [
      ...new Set(
        subscriptionIdsWithRemovedMembers.map(
          (member) => member.subscription_id,
        ),
      ),
    ];
    console.log(
      'Subscriptions with removed members to process:',
      subscriptionIds,
    );

    await this.prisma.transactions.deleteMany({
      where: {
        member: {
          status: subscriptionUserStatus.removed,
          next_due_date: {
            lt: today,
          },
        },
      },
    });

    // delete removed subscriptions
    await this.prisma.subscription_user.deleteMany({
      where: {
        status: subscriptionUserStatus.removed,
        next_due_date: {
          lt: today,
        },
      },
    });

    await this.subscriptionCalculationService.batchCalculateAndUpdateOwnerShareAndMemberCount(
      subscriptionIds,
    );
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async sendRemoveRemindersToOwners() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Calculate reminder dates
    const fiveDaysFromNow = new Date(today);
    fiveDaysFromNow.setDate(today.getDate() + 5);

    const oneDayFromNow = new Date(today);
    oneDayFromNow.setDate(today.getDate() + 1);

    // Find removed members whose next_due_date is exactly 5 days or 1 day from now
    const removedMembers = await this.prisma.subscription_user.findMany({
      where: {
        status: subscriptionUserStatus.removed,
        OR: [
          {
            next_due_date: {
              gte: fiveDaysFromNow,
              lt: new Date(fiveDaysFromNow.getTime() + 24 * 60 * 60 * 1000), // Next day after 5 days
            },
          },
          {
            next_due_date: {
              gte: oneDayFromNow,
              lt: new Date(oneDayFromNow.getTime() + 24 * 60 * 60 * 1000), // Next day after 1 day
            },
          },
        ],
      },
      include: {
        user: { select: { name: true } },
        subscription: {
          include: {
            owner: { select: { email: true, name: true } },
          },
        },
      },
    });

    let processedCount = 0;

    for (const member of removedMembers) {
      if (member.subscription?.owner?.email && member.next_due_date) {
        const daysLeft = Math.ceil(
          (member.next_due_date.getTime() - today.getTime()) /
            (1000 * 60 * 60 * 24),
        );

        await this.engagespotService.send(
          WorkflowIdentifiers.REMOVE_REMINDER_OWNER,
          [member.subscription.owner.email],
          {
            member_name: member.user?.name || member.invited_email,
            subscription_name: member.subscription.name,
            owner_name: member.subscription.owner.name,
            days_left: daysLeft,
            removal_date: member.removed_at?.toLocaleDateString(),
            end_date: member.next_due_date?.toLocaleDateString(),
          },
        );
        processedCount++;
      }
    }

    return { processed: processedCount };
  }
}
