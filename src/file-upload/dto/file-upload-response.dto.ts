import { ApiProperty } from '@nestjs/swagger';

export class FileUploadResponseDto {
  @ApiProperty({
    example: 'uploads/1640995200000-example.jpg',
    description: 'Unique file key/path in S3 storage',
  })
  key: string;

  @ApiProperty({
    example:
      'https://your-bucket.s3.amazonaws.com/uploads/1640995200000-example.jpg?X-Amz-Algorithm=...',
    description:
      'Pre-signed URL to access the uploaded file (expires in 1 hour)',
  })
  url: string;

  @ApiProperty({
    example: 'image/jpeg',
    description: 'MIME type of the uploaded file',
  })
  contentType: string;

  @ApiProperty({
    example: 1024000,
    description: 'File size in bytes',
  })
  size: number;
}

export class FileDeleteResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'file deleted from s3 storage',
    description: 'Success message',
  })
  message: string;
}

export class SignedUrlResponseDto {
  @ApiProperty({
    example:
      'https://your-bucket.s3.amazonaws.com/uploads/1640995200000-example.jpg?X-Amz-Algorithm=...',
    description: 'Pre-signed URL to access the file',
  })
  url: string;
}

export class FileUploadRequestDto {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'File to upload (multipart/form-data)',
  })
  file: Express.Multer.File;
}

export class SignedUrlQueryDto {
  @ApiProperty({
    example: 3600,
    description: 'URL expiration time in seconds (default: 3600 = 1 hour)',
    required: false,
    minimum: 1,
    maximum: 604800, // 7 days max
  })
  expiresIn?: number;
}

export class FileKeyParamDto {
  @ApiProperty({
    example: 'uploads/1640995200000-example.jpg',
    description: 'File key/path in S3 storage',
  })
  key: string;
}
