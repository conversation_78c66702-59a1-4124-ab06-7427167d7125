import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { AWS_CONFIG } from './constants/aws.constants';
import { ResponseService } from 'src/common/services/response.service';

interface FileDetails {
  key: string;
  url: string;
  contentType: string;
  size: number;
}

@Injectable()
export class FileUploadService {
  private s3Client: S3Client;

  constructor(private readonly responseService: ResponseService) {
    // Validate AWS credentials before creating S3 client
    if (
      !AWS_CONFIG.ACCESS_KEY_ID ||
      !AWS_CONFIG.SECRET_ACCESS_KEY ||
      !AWS_CONFIG.BUCKET_NAME ||
      !AWS_CONFIG.REGION
    ) {
      throw new Error(
        'AWS credentials are not configured. Please provide AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.',
      );
    }

    this.s3Client = new S3Client({
      region: AWS_CONFIG.REGION,
      credentials: {
        accessKeyId: AWS_CONFIG.ACCESS_KEY_ID,
        secretAccessKey: AWS_CONFIG.SECRET_ACCESS_KEY,
      },
    });
  }

  async uploadFile(file: Express.Multer.File): Promise<FileDetails> {
    const key = `uploads/${Date.now()}-${file.originalname}`;

    const putCommand = new PutObjectCommand({
      Bucket: AWS_CONFIG.BUCKET_NAME,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: 'private',
    });

    try {
      await this.s3Client.send(putCommand);

      // Generate the URL
      const getCommand = new GetObjectCommand({
        Bucket: AWS_CONFIG.BUCKET_NAME,
        Key: key,
      });
      const url = await getSignedUrl(this.s3Client, getCommand, {
        expiresIn: 3600,
      });

      return {
        key: key,
        url: url,
        contentType: file.mimetype,
        size: file.size,
      };
    } catch (error) {
      throw new Error(`Failed to upload file to S3: ${error.message}`);
    }
  }

  async deleteFile(key: string): Promise<any> {
    const deleteCommand = new DeleteObjectCommand({
      Bucket: AWS_CONFIG.BUCKET_NAME,
      Key: key,
    });

    try {
      await this.s3Client.send(deleteCommand);
    } catch (error) {
      throw new Error(`Failed to delete file from S3: ${error.message}`);
    }
    return this.responseService.successResponse('file deleted from s3 storage');
  }

  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    const getCommand = new GetObjectCommand({
      Bucket: AWS_CONFIG.BUCKET_NAME,
      Key: key,
    });

    try {
      return await getSignedUrl(this.s3Client, getCommand, { expiresIn });
    } catch (error) {
      throw new Error(`Failed to generate signed URL: ${error.message}`);
    }
  }
}
