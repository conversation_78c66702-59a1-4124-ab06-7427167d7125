import {
  Controller,
  Post,
  Delete,
  Get,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import {
  FileUploadResponseDto,
  FileDeleteResponseDto,
  SignedUrlResponseDto,
} from './dto/file-upload-response.dto';

@ApiTags('file-upload')
@Controller('file-upload')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Upload a file to S3 storage',
    description:
      'Uploads a file to AWS S3 and returns file details with a pre-signed URL',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File to upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload (any format)',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'File uploaded successfully',
    type: FileUploadResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - No file provided or invalid file',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'No file provided' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error - Failed to upload to S3',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Failed to upload file to S3: Access Denied',
        },
        error: { type: 'string', example: 'Internal Server Error' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<FileUploadResponseDto> {
    return await this.fileUploadService.uploadFile(file);
  }

  @Delete(':key')
  @ApiOperation({
    summary: 'Delete a file from S3 storage',
    description: 'Deletes a file from AWS S3 using the file key',
  })
  @ApiParam({
    name: 'key',
    description:
      'File key/path in S3 storage (e.g., uploads/1640995200000-example.jpg)',
    example: 'uploads/1640995200000-example.jpg',
  })
  @ApiResponse({
    status: 200,
    description: 'File deleted successfully',
    type: FileDeleteResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'File not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error - Failed to delete from S3',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Failed to delete file from S3: Access Denied',
        },
        error: { type: 'string', example: 'Internal Server Error' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async deleteFile(@Param('key') key: string): Promise<FileDeleteResponseDto> {
    return await this.fileUploadService.deleteFile(key);
  }

  @Get('signed-url/:key')
  @ApiOperation({
    summary: 'Generate a pre-signed URL for file access',
    description:
      'Generates a pre-signed URL to access a file stored in S3 with optional expiration time',
  })
  @ApiParam({
    name: 'key',
    description:
      'File key/path in S3 storage (e.g., uploads/1640995200000-example.jpg)',
    example: 'uploads/1640995200000-example.jpg',
  })
  @ApiQuery({
    name: 'expiresIn',
    description:
      'URL expiration time in seconds (default: 3600 = 1 hour, max: 604800 = 7 days)',
    example: 3600,
    required: false,
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Pre-signed URL generated successfully',
    type: SignedUrlResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'File not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'File not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error - Failed to generate signed URL',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Failed to generate signed URL: Access Denied',
        },
        error: { type: 'string', example: 'Internal Server Error' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async getSignedUrl(
    @Param('key') key: string,
    @Query('expiresIn') expiresIn?: number,
  ): Promise<SignedUrlResponseDto> {
    const url = await this.fileUploadService.getSignedUrl(key, expiresIn);
    return { url };
  }
}
