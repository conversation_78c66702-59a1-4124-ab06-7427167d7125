import {
  Controller,
  Post,
  Delete,
  Get,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';

interface FileUploadResponse {
  key: string;
  url: string;
  contentType: string;
  size: number;
}

@ApiTags('file-upload')
@Controller('file-upload')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<FileUploadResponse> {
    return await this.fileUploadService.uploadFile(file);
  }

  @Delete(':key')
  async deleteFile(@Param('key') key: string): Promise<void> {
    await this.fileUploadService.deleteFile(key);
  }

  @Get('signed-url/:key')
  async getSignedUrl(
    @Param('key') key: string,
    @Query('expiresIn') expiresIn?: number,
  ): Promise<{ url: string }> {
    const url = await this.fileUploadService.getSignedUrl(key, expiresIn);
    return { url };
  }
}
