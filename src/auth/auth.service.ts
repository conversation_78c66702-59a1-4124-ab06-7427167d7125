import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { LoginDto } from './dto/login.dto';
import * as bcrypt from 'bcrypt';
import { TokenService } from './services/jwt.service';
import { jwtConstants } from './constants/constants';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { WorkflowIdentifiers } from 'src/engagespot/constants/workflow.constants';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { subscriptionUserStatus } from '@prisma/client';
import { GoogleLoginDto } from './dto/google-login.dto';
import axios from 'axios';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class AuthService {
  public frontEntUrl: string;
  private readonly maxLoginAttempts: number;
  private readonly lockDurationMinutes: number;

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private tokenService: TokenService,
    private engagespotService: EngagespotService,
    private fileUploadService: FileUploadService,
    private userService: UsersService, // Inject OtpService
  ) {
    // Set defaults from environment variables
    this.frontEntUrl =
      process.env.FRONTEND_URL || 'https://feemates.netlify.app';
    this.maxLoginAttempts = Number(process.env.MAX_LOGIN_ATTEMPTS || '5');
    this.lockDurationMinutes = Number(process.env.LOGIN_LOCK_DURATION || '15');
  }

  async login(loginDto: LoginDto, appType: string) {
    let user = await this.prisma.user.findUnique({
      where: { email: loginDto.email },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      throw new UnauthorizedException('Incorrect email or password');
    }

    // If lock period has expired, reset attempts
    if (user.lock_until && user.lock_until <= new Date()) {
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          failed_login_attempts: 0,
          lock_until: null,
        },
      });

      // Refetch user for fresh validation
      const updatedUser = await this.prisma.user.findUnique({
        where: { id: user.id },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      if (!updatedUser) {
        throw new UnauthorizedException('User not found');
      }
      user = updatedUser;
    }

    if (!user.password || (appType && appType != 'user')) {
      throw new UnauthorizedException('Incorrect email or password');
    }

    // Check if account is still locked
    if (user.lock_until && user.lock_until > new Date()) {
      const remainingTime = Math.ceil(
        (user.lock_until.getTime() - new Date().getTime()) / 60000,
      );
      throw new UnauthorizedException(
        `Account temporarily locked. Try again in ${remainingTime} minutes`,
      );
    }

    const isPasswordValid = await bcrypt.compare(
      loginDto.password,
      user.password,
    );

    if (!isPasswordValid) {
      // Increment failed attempts
      const updatedAttempts = (user.failed_login_attempts || 0) + 1;

      // If max attempts reached, lock the account
      if (updatedAttempts > this.maxLoginAttempts) {
        const lockUntil = new Date(
          Date.now() + this.lockDurationMinutes * 60000,
        );
        await this.prisma.user.update({
          where: { id: user.id },
          data: {
            failed_login_attempts: updatedAttempts,
            lock_until: lockUntil,
          },
        });
        throw new UnauthorizedException(
          `Account temporarily locked. Try again in ${this.lockDurationMinutes} minutes`,
        );
      }

      // Update failed attempts
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          failed_login_attempts: updatedAttempts,
        },
      });

      throw new UnauthorizedException('Incorrect email or password');
    }

    // Reset failed attempts on successful login
    await this.prisma.user.update({
      where: { id: user.id },
      data: {
        failed_login_attempts: 0,
        lock_until: null,
      },
    });
    let tokens;
    if (!user.email_verified_at) {
      const otp = await this.userService.generateOtpAndSend(user);
      tokens = null;
    } else {
      // Generate tokens using TokenService
      tokens = this.tokenService.generateTokens({
        sub: user.id,
        email: user.email,
        roles: user.userRoles.map((ur) => ur.role.name),
      });
    }

    return {
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        is_kyc_verified: user.is_kyc_verified,
        status: user.status,
        roles: user.userRoles.map((ur) => ur.role.name),
        is_otp_verified: user.email_verified_at ? true : false,
      },
      invited_subscriptions: await this.prisma.subscription_user.count({
        where: {
          user_id: user.id,
          status: subscriptionUserStatus.invited,
        },
      }),
      ...(tokens ? tokens : {}),
    };
  }

  async me(userId: number) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Get signed URL for avatar if it exists
    let avatarUrl: string | undefined = undefined;
    if (user.avatar) {
      try {
        avatarUrl = await this.fileUploadService.getSignedUrl(user.avatar);
      } catch (error) {
        console.error('Error getting avatar signed URL:', error);
      }
    }

    // Remove sensitive data
    user.password = null;

    return {
      message: 'Auth user',
      user: {
        ...user,
        avatar: avatarUrl,
      },
    };
  }

  async refreshTokens(refreshToken: string) {
    try {
      // Verify the refresh token
      const payload = await this.jwtService.verify(refreshToken, {
        secret: jwtConstants.refreshSecret,
      });

      // Find the user
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Generate new tokens
      const tokens = this.tokenService.generateTokens({
        sub: user.id,
        email: user.email,
        roles: user.userRoles.map((ur) => ur.role.name),
      });

      return {
        message: 'Tokens refreshed successfully',
        ...tokens,
      };
    } catch (error) {
      if (
        error.name === 'JsonWebTokenError' ||
        error.name === 'TokenExpiredError'
      ) {
        throw new UnauthorizedException('Invalid or expired refresh token');
      }
      throw error;
    }
  }

  async forgotPassword(email: string) {
    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('No account found with that email address.');
    }

    // if (user.google_id) {
    //   throw new BadRequestException(
    //     'This email is registered with Google account. Please use the "Login with Google" option.',
    //   );
    // }

    // Generate reset token
    const resetToken = this.tokenService.generateTokens({
      sub: user.id,
      email: user.email,
      type: 'password_reset',
    });

    // Create reset URL
    const resetUrl = `${this.frontEntUrl}/reset-password?token=${resetToken.access_token}&uid=${user.id}`;

    await this.engagespotService.createOrUpdateUser(user.email, {
      name: user.name || undefined,
      email: user.email,
    });

    await this.engagespotService.send(
      WorkflowIdentifiers.FORGOT_PASSWORD,
      [user.email],
      { resetUrl, name: user.name },
    );

    return {
      message: 'A reset link has been sent to your email.',
      resetUrl,
    };
  }

  async resetPassword(token: string, userId: number, newPassword: string) {
    try {
      // Verify token
      const payload = await this.jwtService.verify(token);

      if (payload.sub !== userId || payload.type !== 'password_reset') {
        throw new UnauthorizedException('Invalid token');
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update password
      await this.prisma.user.update({
        where: { id: userId },
        data: { password: hashedPassword },
      });

      return {
        message: 'Your password has been reset successfully.',
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
  async googleLogin(googleSocialLoginDto: GoogleLoginDto) {
    const { access_token } = googleSocialLoginDto;
    let user;
    try {
      // const oAuth2Client = new OAuth2Client(
      //   this.google_client_id,
      //   this.google_client_secret,
      // );

      const { data: userInfo } = await axios.get(
        'https://www.googleapis.com/oauth2/v3/userinfo',
        {
          headers: {
            Authorization: `Bearer ${access_token}`,
          },
        },
      );

      // Use accessToken as idToken since we're using OAuth
      const payload = userInfo;
      if (!payload || !payload.email) {
        throw new BadRequestException(
          'Email access required. Please grant permission and try again',
        );
      }
      const email = payload.email;

      user = await this.prisma.user.findUnique({
        where: { email, deletedAt: null },
        include: {
          userRoles: {
            include: { role: true },
          },
        },
      });

      // if (user && user.google_id === null) {
      //   throw new BadRequestException(
      //     'Google login failed. Please try again or use email/password.',
      //   );
      // }

      if (!user) {
        let userRole = await this.prisma.role.findFirst({
          where: { name: 'user' },
        });

        if (!userRole) {
          userRole = await this.prisma.role.create({
            data: {
              name: 'user',
            },
          });
        }

        user = await this.prisma.user.create({
          data: {
            email_verified_at: new Date(),
            google_id: payload.sub,
            name: payload.name,
            email: email,
            userRoles: {
              create: [{ role_id: userRole.id }],
            },
          },
          select: {
            id: true,
            name: true,
            email: true,
            is_kyc_verified: true,
            status: true,
            createdAt: true,
            userRoles: {
              select: {
                id: true,
                role: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        });
        await this.engagespotService.createOrUpdateUser(user.email, {
          name: user.name || undefined,
          email: user.email,
        });
      } else {
        await this.prisma.user.update({
          where: {
            id: user.id,
          },
          data: {
            email_verified_at: new Date(),
          },
        });
      }
      await this.prisma.subscription_user.updateMany({
        where: {
          invited_email: user.email,
        },
        data: {
          user_id: user.id,
        },
      });

      const tokens = this.tokenService.generateTokens({
        sub: user.id,
        email: user.email,
        roles: user.userRoles.map((ur) => ur.role.name),
      });
      return {
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          is_kyc_verified: user.is_kyc_verified,
          status: user.status,
          roles: user.userRoles.map((ur) => ur.role.name),
          is_otp_verified: user.email_verified_at ? true : false,
        },
        invited_subscriptions: await this.prisma.subscription_user.count({
          where: {
            user_id: user.id,
            status: subscriptionUserStatus.invited,
          },
        }),
        ...tokens,
      };
    } catch (error) {
      console.log(error);
      throw new BadRequestException(
        'Google login failed. Please try again or use email/password.',
      );
    }
  }
}
