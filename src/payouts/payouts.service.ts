import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { PayoutQueryDto } from './dto/payout-query.dto';
import { Payout, PayoutStatus } from '@prisma/client';

@Injectable()
export class PayoutsService {
  constructor(private prisma: PrismaService) {}

  async findAllForUser(
    userId: number,
    query: PayoutQueryDto,
  ): Promise<{ data: Payout[]; total: number; page: number; limit: number }> {
    const { page = 1, limit = 10, status } = query;
    const skip = (page - 1) * limit;

    const where = {
      user_id: userId,
      ...(status && { status }),
    };

    const [payouts, total] = await Promise.all([
      this.prisma.payout.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.payout.count({ where }),
    ]);

    return {
      data: payouts,
      total,
      page,
      limit,
    };
  }

  async findAllForAdmin(
    query: PayoutQueryDto,
  ): Promise<{ data: Payout[]; total: number; page: number; limit: number }> {
    const { page = 1, limit = 10, status } = query;
    const skip = (page - 1) * limit;

    const where = status ? { status } : {};

    const [payouts, total] = await Promise.all([
      this.prisma.payout.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
            },
          },
        },
      }),
      this.prisma.payout.count({ where }),
    ]);

    return {
      data: payouts,
      total,
      page,
      limit,
    };
  }

  async findOne(id: number, userId?: number): Promise<Payout> {
    const where = {
      id,
      ...(userId && { user_id: userId }),
    };

    return this.prisma.payout.findFirstOrThrow({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
          },
        },
      },
    });
  }

  async isUserAdmin(userId: number): Promise<boolean> {
    const userRoles = await this.prisma.user_role.findMany({
      where: { user_id: userId },
      include: { role: true },
    });
    return userRoles.some((ur) => ur.role.name === 'admin');
  }
}
