import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
  NotFoundException,
} from '@nestjs/common';
import { PayoutsService } from './payouts.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { PayoutQueryDto } from './dto/payout-query.dto';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';

@ApiTags('payouts')
@Controller('payouts')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class PayoutsController {
  constructor(private readonly payoutsService: PayoutsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all payouts for the authenticated user' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['pending', 'in_transit', 'paid', 'failed', 'canceled'],
  })
  async findAll(@Request() req, @Query() query: PayoutQueryDto) {
    return this.payoutsService.findAllForUser(req.user.id, query);
  }

  @Get('admin')
  @Roles('super_admin')
  @ApiOperation({ summary: 'Get all payouts (admin only)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['pending', 'in_transit', 'paid', 'failed', 'canceled'],
  })
  async findAllAdmin(@Query() query: PayoutQueryDto) {
    return this.payoutsService.findAllForAdmin(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific payout' })
  async findOne(@Param('id', ParseIntPipe) id: number, @Request() req) {
    try {
      // If user is admin, they can view any payout, otherwise only their own
      const isAdmin = await this.payoutsService.isUserAdmin(req.user.id);
      const payout = await this.payoutsService.findOne(
        id,
        isAdmin ? undefined : req.user.id,
      );
      return payout;
    } catch (error) {
      throw new NotFoundException('Payout not found');
    }
  }
}
