import { AppService } from './app.service';
import { Controller, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('/time')
  getHello() {
    return this.appService.getHello();
  }

  @Get('swagger/openapi.json')
  getOpenApiJson(@Res() res: Response): void {
    const filePath = path.join(process.cwd(), 'docs', 'openapi.json');
    if (fs.existsSync(filePath)) {
      res.sendFile(filePath);
    } else {
      res.status(404).json({ message: 'OpenAPI JSON not found' });
    }
  }
}
