import { Test, TestingModule } from '@nestjs/testing';
import { SubscriptionTemplatesService } from './subscription_templates.service';

describe('SubscriptionTemplatesService', () => {
  let service: SubscriptionTemplatesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SubscriptionTemplatesService],
    }).compile();

    service = module.get<SubscriptionTemplatesService>(
      SubscriptionTemplatesService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
