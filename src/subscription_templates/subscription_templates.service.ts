import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateSubscriptionTemplateDto } from './dto/create-subscription_template.dto';
import { UpdateSubscriptionTemplateDto } from './dto/update-subscription_template.dto';
import { FileUploadService } from '../file-upload/file-upload.service';
import { PaginationQueryDto } from './dto/pagination-query.dto';
import { ResponseService } from '../common/services/response.service';

@Injectable()
export class SubscriptionTemplatesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileUploadService: FileUploadService,
    private readonly responseService: ResponseService,
  ) {}

  async create(createSubscriptionTemplateDto: CreateSubscriptionTemplateDto) {
    const template = await this.prisma.subscription_template.create({
      data: createSubscriptionTemplateDto,
    });
    return this.responseService.successResponse(
      'Template created successfully',
      template,
    );
  }

  async findAll(query: PaginationQueryDto) {
    const { page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const [templates, total] = await Promise.all([
      this.prisma.subscription_template.findMany({
        skip,
        take: limit,
      }),
      this.prisma.subscription_template.count(),
    ]);

    const templatesWithUrls = await Promise.all(
      templates.map(async (template) => ({
        ...template,
        thumbnail: template.thumbnail
          ? await this.fileUploadService.getSignedUrl(template.thumbnail)
          : null,
        thumbnail_key: template.thumbnail,
      })),
    );

    return {
      data: templatesWithUrls,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const template = await this.prisma.subscription_template.findUnique({
      where: { id },
    });

    if (!template) {
      return this.responseService.errorResponse('Template not found');
    }
    template['thumbnail_key'] = null;
    if (template.thumbnail) {
      template['thumbnail_key'] = template.thumbnail;

      template.thumbnail = await this.fileUploadService.getSignedUrl(
        template.thumbnail,
      );
    }

    return this.responseService.successResponse(
      'Template retrieved successfully',
      template,
    );
  }

  async update(
    id: number,
    updateSubscriptionTemplateDto: UpdateSubscriptionTemplateDto,
  ) {
    try {
      const template = await this.prisma.subscription_template.update({
        where: { id },
        data: updateSubscriptionTemplateDto,
      });
      return this.responseService.successResponse(
        'Template updated successfully',
        template,
      );
    } catch (error) {
      return this.responseService.errorResponse('Template not found');
    }
  }

  async remove(id: number) {
    try {
      await this.prisma.subscription_template.delete({
        where: { id },
      });
      return this.responseService.successResponse(
        'Template deleted successfully',
      );
    } catch (error) {
      return this.responseService.errorResponse('Template not found');
    }
  }

  async seedDefaultData() {
    const defaultData = [
      { name: 'Netflix', description: 'Streaming service', price: 150 },
      { name: 'Amazon', description: 'E-commerce and streaming', price: 200 },
      // { name: 'Spotify', description: 'Music streaming', price: 250 },
      // { name: 'Chat GPT', description: 'AI chatbot service', price: 2000 },
    ];

    // Fetch existing template names
    const existingTemplates = await this.prisma.subscription_template.findMany({
      where: {
        name: { in: defaultData.map((d) => d.name) },
      },
      select: { name: true },
    });
    const existingNames = new Set(existingTemplates.map((t) => t.name));

    // Filter out templates with existing names
    const newTemplates = defaultData.filter((d) => !existingNames.has(d.name));

    if (newTemplates.length > 0) {
      await this.prisma.subscription_template.createMany({
        data: newTemplates,
        skipDuplicates: true,
      });
    }

    return this.responseService.successResponse(
      'Default templates created successfully',
    );
  }
}
