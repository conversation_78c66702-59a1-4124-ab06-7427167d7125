import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SubscriptionTemplatesService } from './subscription_templates.service';
import { CreateSubscriptionTemplateDto } from './dto/create-subscription_template.dto';
import { UpdateSubscriptionTemplateDto } from './dto/update-subscription_template.dto';
import { PaginationQueryDto } from './dto/pagination-query.dto';
import {
  SubscriptionTemplateCreateResponseDto,
  SubscriptionTemplateListResponseDto,
  SubscriptionTemplateUpdateResponseDto,
  SubscriptionTemplateDeleteResponseDto,
  SubscriptionTemplateSeedResponseDto,
} from './dto/subscription-template-response.dto';

@ApiTags('subscription-templates')
@Controller('subscription-templates')
export class SubscriptionTemplatesController {
  constructor(
    private readonly subscriptionTemplatesService: SubscriptionTemplatesService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a subscription template' })
  @ApiResponse({
    status: 201,
    description: 'Template successfully created',
    type: SubscriptionTemplateCreateResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'array',
          items: { type: 'string' },
          example: ['name should not be empty'],
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  create(@Body() createSubscriptionTemplateDto: CreateSubscriptionTemplateDto) {
    return this.subscriptionTemplatesService.create(
      createSubscriptionTemplateDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all subscription templates with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of subscription templates',
    type: SubscriptionTemplateListResponseDto,
  })
  findAll(@Query() query: PaginationQueryDto) {
    return this.subscriptionTemplatesService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a subscription template by id' })
  @ApiResponse({
    status: 200,
    description: 'Template found successfully',
    type: SubscriptionTemplateUpdateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Template not found',
    schema: {
      properties: {
        status: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Template not found' },
      },
    },
  })
  findOne(@Param('id') id: string) {
    return this.subscriptionTemplatesService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a subscription template' })
  @ApiResponse({
    status: 200,
    description: 'Template updated successfully',
    type: SubscriptionTemplateUpdateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Template not found',
    schema: {
      properties: {
        status: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Template not found' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'array',
          items: { type: 'string' },
          example: ['name should not be empty'],
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateSubscriptionTemplateDto: UpdateSubscriptionTemplateDto,
  ) {
    return this.subscriptionTemplatesService.update(
      +id,
      updateSubscriptionTemplateDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a subscription template' })
  @ApiResponse({
    status: 200,
    description: 'Template deleted successfully',
    type: SubscriptionTemplateDeleteResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Template not found',
    schema: {
      properties: {
        status: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Template not found' },
      },
    },
  })
  remove(@Param('id') id: string) {
    return this.subscriptionTemplatesService.remove(+id);
  }

  @Post('seed')
  @ApiOperation({ summary: 'Seed default subscription templates' })
  @ApiResponse({
    status: 201,
    description: 'Default templates created successfully',
    type: SubscriptionTemplateSeedResponseDto,
  })
  seedDefaultData() {
    return this.subscriptionTemplatesService.seedDefaultData();
  }
}
