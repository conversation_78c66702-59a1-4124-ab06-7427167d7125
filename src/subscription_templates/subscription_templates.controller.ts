import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { SubscriptionTemplatesService } from './subscription_templates.service';
import { CreateSubscriptionTemplateDto } from './dto/create-subscription_template.dto';
import { UpdateSubscriptionTemplateDto } from './dto/update-subscription_template.dto';
import { PaginationQueryDto } from './dto/pagination-query.dto';

@ApiTags('subscription-templates')
@Controller('subscription-templates')
export class SubscriptionTemplatesController {
  constructor(
    private readonly subscriptionTemplatesService: SubscriptionTemplatesService,
  ) { }

  @Post()
  @ApiOperation({ summary: 'Create a subscription template' })
  @ApiResponse({
    status: 201,
    description: 'Template successfully created',
    schema: {
      properties: {
        status: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Template created successfully' },
        data: { type: 'object' },
      },
    },
  })
  create(@Body() createSubscriptionTemplateDto: CreateSubscriptionTemplateDto) {
    return this.subscriptionTemplatesService.create(
      createSubscriptionTemplateDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all subscription templates with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of subscription templates',
    schema: {
      properties: {
        status: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Templates retrieved successfully',
        },
        data: {
          type: 'object',
          properties: {
            data: { type: 'array', items: { type: 'object' } },
            meta: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' },
                totalPages: { type: 'number' },
              },
            },
          },
        },
      },
    },
  })
  findAll(@Query() query: PaginationQueryDto) {
    return this.subscriptionTemplatesService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a subscription template by id' })
  @ApiResponse({
    status: 200,
    description: 'Template found successfully',
    schema: {
      properties: {
        status: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Template retrieved successfully' },
        data: { type: 'object' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Template not found',
    schema: {
      properties: {
        status: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Template not found' },
      },
    },
  })
  findOne(@Param('id') id: string) {
    return this.subscriptionTemplatesService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a subscription template' })
  @ApiResponse({
    status: 200,
    description: 'Template updated successfully',
    schema: {
      properties: {
        status: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Template updated successfully' },
        data: { type: 'object' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Template not found',
    schema: {
      properties: {
        status: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Template not found' },
      },
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateSubscriptionTemplateDto: UpdateSubscriptionTemplateDto,
  ) {
    return this.subscriptionTemplatesService.update(
      +id,
      updateSubscriptionTemplateDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a subscription template' })
  @ApiResponse({
    status: 200,
    description: 'Template deleted successfully',
    schema: {
      properties: {
        status: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Template deleted successfully' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Template not found',
    schema: {
      properties: {
        status: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Template not found' },
      },
    },
  })
  remove(@Param('id') id: string) {
    return this.subscriptionTemplatesService.remove(+id);
  }

  @Post('seed')
  @ApiOperation({ summary: 'Seed default subscription templates' })
  @ApiResponse({
    status: 201,
    description: 'Default templates created successfully',
    schema: {
      properties: {
        status: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Default templates created successfully',
        },
      },
    },
  })
  seedDefaultData() {
    return this.subscriptionTemplatesService.seedDefaultData();
  }
}
