import { Module } from '@nestjs/common';
import { SubscriptionTemplatesService } from './subscription_templates.service';
import { SubscriptionTemplatesController } from './subscription_templates.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { FileUploadModule } from '../file-upload/file-upload.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [PrismaModule, FileUploadModule, CommonModule],
  controllers: [SubscriptionTemplatesController],
  providers: [SubscriptionTemplatesService],
})
export class SubscriptionTemplatesModule {}
