import { Test, TestingModule } from '@nestjs/testing';
import { SubscriptionTemplatesController } from './subscription_templates.controller';
import { SubscriptionTemplatesService } from './subscription_templates.service';

describe('SubscriptionTemplatesController', () => {
  let controller: SubscriptionTemplatesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SubscriptionTemplatesController],
      providers: [SubscriptionTemplatesService],
    }).compile();

    controller = module.get<SubscriptionTemplatesController>(
      SubscriptionTemplatesController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
