import { ApiProperty } from '@nestjs/swagger';

export class SubscriptionTemplateResponseDto {
  @ApiProperty({
    example: 1,
    description: 'Template identifier',
  })
  id: number;

  @ApiProperty({
    example: 'Netflix',
    description: 'Template name',
  })
  name: string;

  @ApiProperty({
    example: 'Streaming service for movies and TV shows',
    description: 'Template description',
    required: false,
  })
  description?: string;

  @ApiProperty({
    example: 'https://example.com/netflix-thumbnail.jpg',
    description: 'Template thumbnail URL',
    required: false,
  })
  thumbnail?: string;

  @ApiProperty({
    example: 150,
    description: 'Template price',
    required: false,
  })
  price?: number;
}

export class SubscriptionTemplateCreateResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'Template created successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    type: SubscriptionTemplateResponseDto,
    description: 'Created template data',
  })
  data: SubscriptionTemplateResponseDto;
}

export class SubscriptionTemplateUpdateResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'Template updated successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    type: SubscriptionTemplateResponseDto,
    description: 'Updated template data',
  })
  data: SubscriptionTemplateResponseDto;
}

export class PaginationMetaDto {
  @ApiProperty({
    example: 25,
    description: 'Total number of items',
  })
  total: number;

  @ApiProperty({
    example: 1,
    description: 'Current page number',
  })
  page: number;

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
  })
  limit: number;

  @ApiProperty({
    example: 3,
    description: 'Total number of pages',
  })
  totalPages: number;
}

export class SubscriptionTemplateListDataDto {
  @ApiProperty({
    type: [SubscriptionTemplateResponseDto],
    description: 'Array of subscription templates',
  })
  data: SubscriptionTemplateResponseDto[];

  @ApiProperty({
    type: PaginationMetaDto,
    description: 'Pagination metadata',
  })
  meta: PaginationMetaDto;
}

export class SubscriptionTemplateListResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'Templates retrieved successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    type: SubscriptionTemplateListDataDto,
    description: 'Paginated template data',
  })
  data: SubscriptionTemplateListDataDto;
}

export class SubscriptionTemplateDeleteResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'Template deleted successfully',
    description: 'Success message',
  })
  message: string;
}

export class SubscriptionTemplateSeedResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'Default templates created successfully',
    description: 'Success message',
  })
  message: string;
}
