import { IsString, IsOptional, IsNumber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateSubscriptionTemplateDto {
  @ApiProperty({
    description: 'Template name',
    example: 'Netflix',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Template description',
    example: 'Streaming service for movies and TV shows',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Template thumbnail Key',
    example: 'netflix-thumbnail.jpg',
  })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiPropertyOptional({
    description: 'Template price',
    example: 150,
  })
  @IsOptional()
  @IsNumber()
  price?: number;
}
