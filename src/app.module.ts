import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { EngagespotModule } from './engagespot/engagespot.module';
import { FileUploadModule } from './file-upload/file-upload.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';
import { CommonModule } from './common/common.module';
import { ConfigModule } from '@nestjs/config';
import { SubscriptionUsersModule } from './subscription_users/subscription_users.module';
import { UserKycModule } from './user_kyc/user_kyc.module';
import { StripeModule } from './stripe/stripe.module';
import { PayoutsModule } from './payouts/payouts.module';
import { SubscriptionTemplatesModule } from './subscription_templates/subscription_templates.module';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    ScheduleModule.forRoot(), // <-- Important
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    UsersModule,
    AuthModule,
    EngagespotModule,
    FileUploadModule,
    SubscriptionsModule,
    CommonModule,
    SubscriptionUsersModule,
    UserKycModule,
    StripeModule,
    PayoutsModule,
    SubscriptionTemplatesModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
