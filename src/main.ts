import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';

import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common';
// import { ConfigService } from "@nestjs/config";
import { json } from 'express';
import { useContainer } from 'class-validator';
import { CatchFilter } from './catch.interceptor';
import { ResInterceptor } from './res.interceptor';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Controller, Get, UseGuards } from '@nestjs/common';
import { writeFileSync } from 'fs';
import { join } from 'path';
import * as fs from 'fs';
import * as path from 'path';

@Controller()
class AppController {
  @Get('protected-route')
  async protectedRoute() {
    // Only authenticated users can access this
  }
}

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bodyParser: false,
  });
  // process.env.TZ = "Asia/Kolkata";
  const port = process.env.PORT || 3000;
  app.use(json({ limit: '5mb' }));
  app.enableCors();

  app.setGlobalPrefix('api');

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidUnknownValues: true,
      forbidNonWhitelisted: true,
      stopAtFirstError: true,
    }),
  );

  useContainer(app.select(AppModule), {
    fallbackOnErrors: true,
  });

  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  app.useGlobalInterceptors(new ResInterceptor());
  app.useGlobalFilters(new CatchFilter());

  app.useGlobalFilters();

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('FeeMates API')
    .setDescription('The FeeMates API description')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Generate Swagger UI
  SwaggerModule.setup('api', app, document);

  // Define the Swagger output path
  const swaggerPath = 'docs';

  // Setup Swagger module
  SwaggerModule.setup(swaggerPath, app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
    customSiteTitle: 'FeeMates API Docs',
  });

  // Save OpenAPI document as JSON file
  try {
    // Create directory for OpenAPI document if it doesn't exist
    const docsDir = path.join(process.cwd(), 'docs');
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }

    // Save OpenAPI document to a file
    fs.writeFileSync(
      path.join(docsDir, 'openapi.json'),
      JSON.stringify(document, null, 2),
    );
  } catch (error) {
    console.error('Failed to write OpenAPI document to file:', error);
  }
  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(
    `Swagger documentation is available at: http://localhost:${port}/api`,
  );
}
bootstrap();
