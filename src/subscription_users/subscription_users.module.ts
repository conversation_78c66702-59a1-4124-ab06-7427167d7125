import { Module } from '@nestjs/common';
import { SubscriptionUsersService } from './subscription_users.service';
import { SubscriptionUsersController } from './subscription_users.controller';
import { CommonModule } from 'src/common/common.module';
import { PrismaModule } from 'src/prisma/prisma.module';
import { EngagespotModule } from 'src/engagespot/engagespot.module';
import { StripeModule } from 'src/stripe/stripe.module';
import { FileUploadModule } from 'src/file-upload/file-upload.module';

@Module({
  imports: [
    CommonModule,
    PrismaModule,
    EngagespotModule,
    StripeModule,
    FileUploadModule,
  ],
  controllers: [SubscriptionUsersController],
  providers: [SubscriptionUsersService],
})
export class SubscriptionUsersModule {}
