import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  ConflictException,
} from '@nestjs/common';
import { SubscriptionUsersService } from './subscription_users.service';
import { CreateSubscriptionUserDto } from './dto/create-subscription_user.dto';
import { UpdateSubscriptionUserDto } from './dto/update-subscription_user.dto';
import { InviteUsersDto } from './dto/invite-users.dto';
import { AcceptInvitationDto } from './dto/accept-invitation.dto';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';

@Controller('subscription-invites')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class SubscriptionUsersController {
  constructor(
    private readonly subscriptionUsersService: SubscriptionUsersService,
  ) {}

  @Get('')
  findInvitedSubscriptions(@Request() req) {
    return this.subscriptionUsersService.findInvitedSubscriptions(req.user.id);
  }

  @Get('subscriptions/:subscriptionId/validate')
  validateInvite(
    @Param('subscriptionId') subscriptionId: string,
    @Request() req,
  ) {
    return this.subscriptionUsersService.validateInvite(
      req.user.id,
      +subscriptionId,
    );
  }

  @Post('subscriptions/:subscription_id/invite')
  inviteUsers(@Request() req, @Body() inviteUsersDto: InviteUsersDto) {
    return this.subscriptionUsersService.inviteUsers(
      req.user.id,
      inviteUsersDto,
    );
  }

  @Post(':id/accept')
  @ApiOperation({
    summary:
      'Accept subscription invitation and create Stripe checkout session',
  })
  acceptInvite(
    @Request() req,
    @Param('id') subscriptionInviteId: string,
    @Body() acceptDto: AcceptInvitationDto,
  ) {
    return this.subscriptionUsersService.acceptInvite(
      req.user.id,
      +subscriptionInviteId,
      acceptDto,
    );
  }

  @Post('subscriptions/:subscription_id/accept')
  @ApiOperation({
    summary:
      'Accept subscription invitation and create Stripe checkout session',
  })
  acceptInviteSubscription(
    @Request() req,
    @Param('subscription_id') subscription_id: string,
    @Body() acceptDto: AcceptInvitationDto,
  ) {
    return this.subscriptionUsersService.acceptInvitePublic(
      req.user.id,
      +subscription_id,
      acceptDto,
    );
  }

  @Post(':id/decline')
  declineInvite(@Request() req, @Param('id') subscriptionInviteId: string) {
    return this.subscriptionUsersService.declineInvite(
      req.user.id,
      +subscriptionInviteId,
    );
  }

  @Post(':id/resend-invite')
  resendInvite(@Request() req, @Param('id') subscriptionInviteId: string) {
    return this.subscriptionUsersService.resendInvite(
      req.user.id,
      +subscriptionInviteId,
    );
  }

  @Post('subscriptions/:subscriptionId/cancel')
  cancelInvite(
    @Request() req,
    @Param('subscriptionId') subscriptionId: string,
  ) {
    return this.subscriptionUsersService.cancelInvite(
      req.user.id,
      +subscriptionId,
    );
  }
}
