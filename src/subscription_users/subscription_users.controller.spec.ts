import { Test, TestingModule } from '@nestjs/testing';
import { SubscriptionUsersController } from './subscription_users.controller';
import { SubscriptionUsersService } from './subscription_users.service';

describe('SubscriptionUsersController', () => {
  let controller: SubscriptionUsersController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SubscriptionUsersController],
      providers: [SubscriptionUsersService],
    }).compile();

    controller = module.get<SubscriptionUsersController>(
      SubscriptionUsersController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
