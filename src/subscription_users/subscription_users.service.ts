import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { CreateSubscriptionUserDto } from './dto/create-subscription_user.dto';
import { UpdateSubscriptionUserDto } from './dto/update-subscription_user.dto';
import { InviteUsersDto } from './dto/invite-users.dto';
import { AcceptInvitationDto } from './dto/accept-invitation.dto';
import { PrismaService } from '../prisma/prisma.service';
import { EngagespotService } from '../engagespot/engagespot.service';
import { StripeService } from '../stripe/stripe.service';
import { WorkflowIdentifiers } from '../engagespot/constants/workflow.constants';
import { ResponseService } from 'src/common/services/response.service';
import {
  payment_status,
  SubscriptionStatus,
  subscriptionUserStatus,
  user,
  user_type,
} from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import { addDays } from 'date-fns';
import { User } from 'src/users/entities/user.entity';
import { FileUploadService } from 'src/file-upload/file-upload.service';

interface CountResult {
  count: BigInt;
}

interface SubscriptionInvite {
  id: number;
  subscription_id: number;
  user_id: number | null;
  invited_email: string | null;
  price: number;
  status: subscriptionUserStatus;
  user_type: user_type;
  token?: string;
  token_expires_at?: Date;
  subscription: {
    id: number;
    name: string;
    status: SubscriptionStatus;
    per_person_price: number | null;
    stripe_plan__price_id: string | null;
    deletedAt: Date | null;
    members_count: number;
    max_no_of_participants: number;
    description?: string | null;
    thumbnail?: string | null;
    createdAt?: Date;
  };
}

@Injectable()
export class SubscriptionUsersService {
  public frontEntUrl;
  private readonly tokenExpiryDays: number;

  constructor(
    private prisma: PrismaService,
    private engagespotService: EngagespotService,
    private responseService: ResponseService,
    private stripeService: StripeService,
    private fileUploadService: FileUploadService,
  ) {
    this.frontEntUrl = process.env.FRONTEND_URL;
    this.tokenExpiryDays = Number(process.env.INVITE_TOKEN_EXPIRY_DAYS) || 10;
  }

  async inviteUsers(userId: number, inviteUsersDto: InviteUsersDto) {
    // Verify subscription exists and get details
    const subscription = await this.prisma.subscription.findFirst({
      where: {
        id: inviteUsersDto.subscription_id,
        deletedAt: null,
        created_by: userId,
      },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    if (subscription.created_by != userId) {
      throw new NotFoundException(
        'Only owner can send invite to a subscription',
      );
    }

    if (subscription.status !== SubscriptionStatus.active) {
      throw new BadRequestException(
        'You can only send invites from an active subscription.',
      );
    }

    if (subscription.members_count >= subscription.max_no_of_participants) {
      throw new BadRequestException(
        'The subscription has reached its participant limit.',
      );
    }

    // Fetch all users with given emails in one query
    const existingUsers = await this.prisma.user.findMany({
      where: {
        email: {
          in: inviteUsersDto.emails,
        },
      },
      select: {
        id: true,
        email: true,
        name: true,
      },
    });

    // Create a map of email to user for easy lookup
    const userMap = new Map(existingUsers.map((user) => [user.email, user]));

    // Ensure per_person_price exists and is valid
    if (
      !subscription.per_person_price ||
      typeof subscription.per_person_price !== 'number'
    ) {
      throw new ConflictException('Invalid subscription price configuration');
    }

    // Check for existing subscription users
    const existingSubUsers = await this.prisma.subscription_user.findMany({
      where: {
        subscription_id: subscription.id,
        invited_email: {
          in: inviteUsersDto.emails,
        },
      },
      select: {
        invited_email: true,
        status: true,
        next_due_date: true,
      },
    });

    // ...existing code...
    const existingEmailsTemp = existingSubUsers.map((user) => ({
      email: user.invited_email,
      status: user.status,
      next_due_date: user.next_due_date,
    }));

    if (existingEmailsTemp.length > 0) {
      const subscriptionUser = existingEmailsTemp[0];
      if (subscriptionUser.status === subscriptionUserStatus.removed) {
        throw new ConflictException(
          `This user is already part of the subscription. Their subscription will end on ${subscriptionUser.next_due_date?.toLocaleDateString()}. You can re-invite them only after this date.`,
        );
      }
      throw new ConflictException('User Already in the list');
    }

    const existingEmails = new Set(
      existingSubUsers.map((user) => user.invited_email),
    );
    const newEmails = inviteUsersDto.emails.filter(
      (email) => !existingEmails.has(email),
    );

    if (newEmails.length < 0) {
      throw new ConflictException('No invitations to sent');
    }
    // Prepare subscription user data for batch insert (only for new emails)
    // Generate token and expiry date for invites
    const expiryDate = addDays(new Date(), this.tokenExpiryDays);
    const token = uuidv4();
    const subscriptionUserData = newEmails.map((email) => ({
      subscription_id: subscription.id,
      user_id: userMap.get(email)?.id || null,
      invited_email: email,
      price: Number(subscription.per_person_price),
      status: subscriptionUserStatus.invited,
      user_type: user_type.member,
      token: token,
      token_expires_at: expiryDate,
    }));

    // Batch insert new subscription users
    await this.prisma.subscription_user.createMany({
      data: subscriptionUserData,
      skipDuplicates: true,
    });

    // Get frontend URL and create invite link
    const inviteLink = await this.generateInviteLink(subscription.id, token);
    console.log(inviteLink);
    // Create or update all users in Engagespot
    await Promise.all(
      inviteUsersDto.emails.map((email) =>
        this.engagespotService.createOrUpdateUser(email, {
          email,
          name: userMap.get(email)?.name || undefined,
        }),
      ),
    );

    // Send notifications to all recipients at once
    await this.engagespotService.send(
      WorkflowIdentifiers.SUBSCRIPTION_INVITE,
      newEmails,
      {
        subscription_name: subscription.name,
        owner_name: subscription.owner.name,
        invite_link: inviteLink,
      },
    );
    return {
      message: `Invitation sent`,

      // message: `Invitations sent to ${newEmails.length} ${newEmails.length === 1 ? 'member' : 'members'}`,
    };
  }

  async findInvitedSubscriptions(userId: number) {
    const invitedSubscriptions = await this.prisma.subscription_user.findMany({
      where: {
        user_id: userId,
        OR: [
          {
            status: subscriptionUserStatus.invited,
          },
          {
            status: subscriptionUserStatus.expired,
            joined_at: null,
          },
        ],
      },
      include: {
        subscription: {
          select: {
            id: true,
            name: true,
            members_count: true,
            max_no_of_participants: true,
            owner: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    return this.responseService.successResponse(
      'Invited subscriptions fetched successfully',
      invitedSubscriptions.map((invite) => ({
        subscription_id: invite.subscription.id,
        subscription_name: invite.subscription.name,
        members_count: invite.subscription.members_count,
        max_members_count: invite.subscription.max_no_of_participants,
        owner_name: invite.subscription.owner.name,
        price: invite.price,
        status: invite.status,
        createdAt: invite.createdAt,
        joinedAt: invite.joined_at,
        id: invite.id,
      })),
    );
  }

  async declineInvite(userId: number, subscriptionInviteId: number) {
    const invite = await this.prisma.subscription_user.findFirst({
      where: {
        id: subscriptionInviteId,
        user_id: userId,
        status: subscriptionUserStatus.invited,
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
        subscription: {
          include: {
            owner: {
              select: {
                email: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!invite) {
      throw new NotFoundException('Invite not found or already processed');
    }

    await this.prisma.subscription_user.update({
      where: { id: invite.id },
      data: { status: subscriptionUserStatus.declined },
    });
    await this.engagespotService.send(
      WorkflowIdentifiers.INVITATION_REJECTED,
      [invite.subscription.owner.email],
      {
        subscription_name: invite.subscription?.name,
        owner_name: invite.subscription.owner?.name,
        member_name: invite?.user?.name,
      },
    );
    return this.responseService.successResponse(
      'Invitation declined successfully',
    );
  }

  private async processInviteAcceptance(
    userId: number,
    invite: SubscriptionInvite,
    acceptDto: AcceptInvitationDto,
    user: any,
  ) {
    if (!invite.subscription.stripe_plan__price_id) {
      throw new ConflictException(
        'Subscription not properly configured with Stripe',
      );
    }

    const customerId = await this.stripeService.getOrCreateCustomer(
      user.id,
      user.email,
      user.name || '',
    );

    const session = await this.stripeService.createSubscriptionCheckoutSession(
      customerId,
      invite.subscription.stripe_plan__price_id,
      acceptDto.successUrl,
      acceptDto.cancelUrl,
      invite.subscription_id,
      invite.id,
      invite.price,
      userId,
    );

    return { message: 'Subscription payment created', url: session.url };
  }

  async acceptInvite(
    userId: number,
    subscriptionInviteId: number,
    acceptDto: AcceptInvitationDto,
  ) {
    // Find the invite
    const invite = await this.prisma.subscription_user.findFirst({
      where: {
        id: subscriptionInviteId,
        user_id: userId,
        status: subscriptionUserStatus.invited,
      },
      include: {
        subscription: true,
      },
    });

    if (!invite) {
      throw new NotFoundException('Invite not found or already processed');
    }

    if (
      invite.subscription.deletedAt ||
      invite.subscription.status != SubscriptionStatus.active
    ) {
      throw new ConflictException('Subscription is no longer active');
    }

    if (
      invite.subscription.max_no_of_participants <=
      invite.subscription.members_count
    ) {
      throw new ConflictException(
        'This subscription has reached the maximum number of participants. You cannot join this bundle.',
      );
    }
    // Get user details
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!invite.subscription.stripe_plan__price_id) {
      throw new ConflictException(
        'Subscription not properly configured with Stripe',
      );
    }

    // Get or create Stripe customer
    const customerId = await this.stripeService.getOrCreateCustomer(
      user.id,
      user.email,
      user.name || '',
    );

    // Create checkout session
    const session = await this.stripeService.createSubscriptionCheckoutSession(
      customerId,
      invite.subscription.stripe_plan__price_id,
      acceptDto.successUrl,
      acceptDto.cancelUrl,
      invite.subscription_id,
      invite.id,
      invite.price,
      userId,
    );
    // return this.processInviteAcceptance(userId, invite, acceptDto, user);

    return { message: 'Subscription payment created', url: session.url };
  }

  async acceptInvitePublic(
    userId: number,
    subscription_id: number,
    acceptDto: AcceptInvitationDto,
  ) {
    // Find the subscription first
    const subscription = await this.prisma.subscription.findFirst({
      where: {
        id: Number(subscription_id),
      },
    });

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    if (!subscription.per_person_price) {
      throw new BadRequestException('Invalid subscription price configuration');
    }

    if (
      subscription.deletedAt ||
      subscription.status != SubscriptionStatus.active
    ) {
      throw new ConflictException('Subscription is no longer active');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (subscription.max_no_of_participants <= subscription.members_count) {
      throw new ConflictException(
        'This subscription has reached the maximum number of participants. You cannot join this bundle.',
      );
    }
    // Find existing invite or create new one
    let invite = (await this.prisma.subscription_user.findFirst({
      where: {
        subscription_id,
        user_id: userId,
      },
      include: {
        subscription: true,
      },
    })) as unknown as SubscriptionInvite;

    if (invite && invite.status !== subscriptionUserStatus.invited) {
      throw new NotFoundException('Invite not found or already processed');
    }
    // Verify token if it exists
    if (acceptDto.token) {
      if (
        invite &&
        (acceptDto.token != invite.token ||
          (invite.token_expires_at && invite.token_expires_at < new Date()))
      ) {
        throw new BadRequestException('Invite token has expired');
      }
    }
    if (!invite) {
      // Create new invite
      invite = (await this.prisma.subscription_user.create({
        data: {
          subscription_id,
          user_id: userId,
          invited_email: user.email,
          status: subscriptionUserStatus.pending,
          price: subscription.per_person_price,
          user_type: user_type.member,
          token: uuidv4(),
          token_expires_at: addDays(new Date(), this.tokenExpiryDays),
        },
        include: {
          subscription: true,
        },
      })) as unknown as SubscriptionInvite;
    }

    return this.processInviteAcceptance(userId, invite, acceptDto, user);
  }

  async cancelInvite(userId: number, subscriptionId: number) {
    // Check if user is the subscription owner

    const subscription = await this.prisma.subscription.findFirst({
      where: {
        id: subscriptionId,
        created_by: userId,
      },
    });

    if (!subscription) {
      throw new NotFoundException(
        'Subscription not found or you are not the owner',
      );
    }

    // Find and update the invite
    await this.prisma.subscription_user.updateMany({
      where: {
        subscription_id: subscriptionId,
      },
      data: {
        status: subscriptionUserStatus.cancelled,
      },
    });

    return this.responseService.successResponse(
      'Invite cancelled successfully',
    );
  }

  async validateInvite(userId: number, subscriptionId: number) {
    // Find subscription with owner details

    await this.prisma.subscription_user.deleteMany({
      where: {
        subscription_id: subscriptionId,
        user_id: userId,
        status: subscriptionUserStatus.pending,
      },
    });
    const subscription = await this.prisma.subscription.findFirst({
      where: {
        id: subscriptionId,
        deletedAt: null,
      },
      include: {
        owner: {
          select: {
            name: true,
          },
        },
        members: {
          where: {
            user_id: userId,
          },
          select: {
            id: true,
            status: true,
            createdAt: true,
            joined_at: true,
            price: true,
            user_id: true,
          },
        },
      },
    });

    if (subscription) {
      subscription['is_owner'] =
        subscription.created_by == userId ? true : false;
      const member =
        subscription.members.length > 0 ? subscription.members[0] : null;
      subscription['member'] = member;
      (subscription as any).members && delete (subscription as any).members;
      subscription['thumbnail'] = subscription.thumbnail
        ? await this.fileUploadService.getSignedUrl(subscription.thumbnail)
        : subscription.thumbnail;
      subscription['subscription_invite_id'] = member ? member.id : null;
    }

    return this.responseService.successResponse(
      'Subscription validated',
      subscription,
    );
  }

  async resendInvite(userId: number, subscriptionInviteId: number) {
    const invite = await this.prisma.subscription_user.findFirst({
      where: {
        id: subscriptionInviteId,
        // status: subscriptionUserStatus.active,
      },
      include: {
        subscription: {
          select: {
            id: true,
            name: true,
            owner: true,
          },
        },
      },
    });

    if (!invite) {
      throw new NotFoundException('Invite not found or already processed');
    }
    const token = uuidv4();
    if (invite.status == subscriptionUserStatus.declined) {
      await this.prisma.subscription_user.update({
        where: {
          id: subscriptionInviteId,
        },
        data: {
          status: subscriptionUserStatus.invited,
          token,
          token_expires_at: addDays(new Date(), this.tokenExpiryDays),
        },
      });
    }
    const inviteLink = await this.generateInviteLink(
      invite.subscription_id,
      token,
    );

    // Send notifications to all recipients at once
    if (invite.invited_email) {
      await this.engagespotService.createOrUpdateUser(invite.invited_email, {
        name: undefined,
        email: invite.invited_email,
      });
      await this.engagespotService.send(
        WorkflowIdentifiers.SUBSCRIPTION_INVITE,
        [invite.invited_email],
        {
          subscription_name: invite.subscription.name,
          owner_name: invite.subscription.owner.name,
          invite_link: inviteLink,
        },
      );
    }
    return this.responseService.successResponse(
      'Invitation resend successfully',
    );
  }

  async generateInviteLink(subscription_id: number, token: string) {
    const frontendUrl = this.frontEntUrl;
    return `${frontendUrl}/invites/${subscription_id}?token=${token}`;
  }
}
