import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsUrl } from 'class-validator';

export class AcceptInvitationDto {
  @ApiProperty({
    description: 'Success URL for Stripe checkout',
    example: 'https://example.com/success',
  })
  @IsNotEmpty()
  // @IsUrl()
  successUrl: string;

  @ApiProperty({
    description: 'Cancel URL for Stripe checkout',
    example: 'https://example.com/cancel',
  })
  @IsNotEmpty()
  // @IsUrl()
  cancelUrl: string;

  @IsOptional()
  token: string;

  @IsOptional()
  subscription_invite_id: number;
}
