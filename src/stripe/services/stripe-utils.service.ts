import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { StripeService } from '../stripe.service';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  payment_status,
  stripe_subscription_status,
  subscriptionUserStatus,
  PayoutStatus,
  SubscriptionStatus,
} from '@prisma/client';
import { WorkflowIdentifiers } from 'src/engagespot/constants/workflow.constants';
import { SubscriptionCalculationService } from 'src/common/services/subscription-calculation.service';

@Injectable()
export class StripeUtilsService {
  constructor(
    private readonly engagespotService: EngagespotService,
    @Inject(forwardRef(() => StripeService))
    private readonly stripeService: StripeService,
    private prisma: PrismaService,
    private readonly subscriptionCalculationService: SubscriptionCalculationService,
  ) {}

  async handleCheckoutSessionCompleted(session: any) {
    // Update transaction status to completed
    await this.prisma.transactions.updateMany({
      where: { session_id: session.id },
      data: {
        status: 'completed',
        payment_date: new Date(),
      },
    });

    // Find transaction and update related subscription_user
    if (session.subscription) {
      const transaction = await this.prisma.transactions.findFirst({
        where: { session_id: session.id },
        include: {
          user: {
            select: {
              name: true,
            },
          },
        },
      });

      if (transaction && transaction.subscription_user_id) {
        // Update subscription user status
        // const stripeSubscription = await this.getSubscriptionFromStripe(session.subscription)
        // console.log(stripeSubscription)
        const nextMonth = new Date();
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        const subscription_user = await this.prisma.subscription_user.findFirst(
          {
            where: {
              id: transaction.subscription_user_id,
            },
            select: {
              joined_at: true,
              stripe_subscription_id: true,
            },
          },
        );
        const joined_at = subscription_user?.joined_at ?? null;
        await this.prisma.subscription_user.update({
          where: {
            id: transaction.subscription_user_id,
          },
          data: {
            stripe_subscription_id: session.subscription,
            stripe_subscription_status: 'active' as stripe_subscription_status,
            status: 'active' as subscriptionUserStatus,
            start_date: new Date(),
            next_due_date: nextMonth,
            joined_at: new Date(),
          },
        });

        // Get subscription details and calculate owner_share using the calculation service
        const subscription = await this.prisma.subscription.findFirst({
          where: { id: transaction.subscription_id },
          include: {
            owner: {
              select: {
                email: true,
                name: true,
              },
            },
          },
        });

        if (subscription && subscription.status == SubscriptionStatus.active) {
          // Calculate and update owner_share and member_count using the calculation service
          const calculationResult =
            await this.subscriptionCalculationService.calculateAndUpdateOwnerShareAndMemberCount(
              transaction.subscription_id,
            );
          if (!joined_at || joined_at === null) {
            await this.engagespotService.send(
              WorkflowIdentifiers.INVITATION_ACCEPTED,
              [subscription.owner.email],
              {
                subscription_name: subscription?.name,
                owner_name: subscription.owner?.name,
                member_name: transaction?.user?.name,
                owner_share: calculationResult.owner_share,
              },
            );
            await this.engagespotService.send(
              WorkflowIdentifiers.PAYMENT_RECEIVED,
              [subscription.owner.email],
              {
                subscription_name: subscription?.name,
                owner_name: subscription.owner?.name,
                member_name: transaction?.user?.name,
                owner_share: calculationResult.owner_share,
              },
            );
            if (
              calculationResult.members_count >=
              subscription.max_no_of_participants
            ) {
              await this.engagespotService.send(
                WorkflowIdentifiers.SUBSCRIPTION_FULL,
                [subscription.owner.email],
                {
                  subscription_name: subscription?.name,
                  owner_name: subscription.owner?.name,
                },
              );
            }
          }
        }
        if (subscription_user?.stripe_subscription_id) {
          await this.syncSubscriptionDetailsWithStripe(
            subscription_user.stripe_subscription_id,
          );
        }
      }
    }
  }

  async handleSubscriptionUpdated(subscription: any) {
    const status = subscription.status;
    const stripeSubscriptionId = subscription.id;
    await this.syncSubscriptionDetailsWithStripe(stripeSubscriptionId);
  }

  async handleSubscriptionDeleted(subscription: any) {
    const stripeSubscriptionId = subscription.id;
    const cancelDate = new Date();

    await this.prisma.subscription_user.updateMany({
      where: { stripe_subscription_id: stripeSubscriptionId },
      data: {
        stripe_subscription_status: 'cancelled',
        // status: 'cancelled',
        cancelled_date: cancelDate,
        cancellation_effective_date: new Date(subscription.cancel_at * 1000),
      },
    });
    await this.syncSubscriptionDetailsWithStripe(stripeSubscriptionId);
  }

  async handleInvoicePaid(session: any) {
    const stripeSubscriptionId = session.subscription;
    const userSubscription =
      await this.findUserSubscriptionWithStripeSubscriptionId(
        stripeSubscriptionId,
      );
    if (userSubscription) {
      // Create transaction record
      await this.prisma.transactions.create({
        data: {
          subscription_id: userSubscription.subscription_id,
          subscription_user_id: userSubscription.id,
          amount: userSubscription.price,
          status: payment_status.completed,
          payment_date: new Date(),
        },
      });

      // Send payment completed notification
      if (userSubscription.user?.email) {
        await this.engagespotService.send(
          WorkflowIdentifiers.PAYMENT_COMPLETED,
          [userSubscription.user?.email],
          {
            subscription_name: userSubscription.subscription?.name,
            member_name: userSubscription.user.name,
          },
        );
      }
    }

    await this.syncSubscriptionDetailsWithStripe(stripeSubscriptionId);
  }

  async handleInvoiceFailed(session: any) {
    const stripeSubscriptionId = session.subscription;
    const userSubscription =
      await this.findUserSubscriptionWithStripeSubscriptionId(
        stripeSubscriptionId,
      );
    if (userSubscription) {
      await this.prisma.transactions.create({
        data: {
          subscription_id: userSubscription.subscription_id,
          subscription_user_id: userSubscription.id,
          amount: userSubscription.price,
          status: payment_status.failed,
        },
      });
    }

    await this.syncSubscriptionDetailsWithStripe(stripeSubscriptionId);
  }

  async handlePayoutCreated(payout: any) {
    const user = await this.prisma.user.findFirst({
      where: { stripe_connect_account_id: payout.destination },
    });

    if (user) {
      await this.prisma.payout.create({
        data: {
          user_id: user.id,
          amount: payout.amount,
          currency: payout.currency,
          destination: payout.destination,
          arrival_date: new Date(payout.arrival_date * 1000),
          balance_transaction_id: payout.balance_transaction,
          statement_descriptor: payout.statement_descriptor,
          status: 'pending',
          type: payout.type,
          metadata: payout.metadata || {},
        },
      });
    }
  }

  async handlePayoutPaid(payout: any) {
    await this.prisma.payout.updateMany({
      where: { balance_transaction_id: payout.balance_transaction },
      data: {
        status: 'paid' as PayoutStatus,
      },
    });

    // Notify user about successful payout
    const payoutRecord = await this.prisma.payout.findFirst({
      where: { balance_transaction_id: payout.balance_transaction },
      include: { user: true },
    });

    if (payoutRecord?.user?.email) {
      await this.engagespotService.send(
        WorkflowIdentifiers.PAYOUT_SUCCESSFUL,
        [payoutRecord.user.email],
        {
          amount: (payout.amount / 100).toFixed(2),
          currency: payout.currency.toUpperCase(),
        },
      );
    }
  }

  async handlePayoutFailed(payout: any) {
    await this.prisma.payout.updateMany({
      where: { balance_transaction_id: payout.balance_transaction },
      data: {
        status: 'failed' as PayoutStatus,
        failure_code: payout.failure_code,
        failure_message: payout.failure_message,
      },
    });

    // Notify user about failed payout
    const payoutRecord = await this.prisma.payout.findFirst({
      where: { balance_transaction_id: payout.balance_transaction },
      include: { user: true },
    });

    if (payoutRecord?.user?.email) {
      await this.engagespotService.send(
        WorkflowIdentifiers.PAYOUT_FAILED,
        [payoutRecord.user.email],
        {
          amount: (payout.amount / 100).toFixed(2),
          currency: payout.currency.toUpperCase(),
          reason: payout.failure_message,
        },
      );
    }
  }

  async syncSubscriptionDetailsWithStripe(stripe_subscription_id: string) {
    const stripe_subscription =
      await this.stripeService.getSubscriptionFromStripe(
        stripe_subscription_id,
      );
    const subscription_user = await this.prisma.subscription_user.findFirst({
      where: { stripe_subscription_id: stripe_subscription_id },
    });
    let data: any = {
      stripe_subscription_status: stripe_subscription.status,
      status: subscription_user?.removed_at
        ? subscriptionUserStatus.removed
        : subscription_user?.status === subscriptionUserStatus.expired
          ? subscriptionUserStatus.expired
          : this.mapStripeStatusToUserStatus(stripe_subscription.status),
      start_date: new Date(stripe_subscription.start_date * 1000),
    };

    // Handle next_due_date from items.data[0].current_period_end if it exists
    if (
      stripe_subscription.items &&
      stripe_subscription.items.data &&
      stripe_subscription.items.data.length > 0 &&
      stripe_subscription.items.data[0].current_period_end
    ) {
      // console.log('here')
      data.next_due_date = new Date(
        stripe_subscription.items.data[0].current_period_end * 1000,
      );
    }

    if (stripe_subscription.canceled_at) {
      data = {
        ...data,
        cancellation_effective_date: new Date(
          stripe_subscription.canceled_at * 1000,
        ),
      };
    }

    await this.prisma.subscription_user.updateMany({
      where: { stripe_subscription_id: stripe_subscription_id },
      data,
    });
    // console.log('data', data);
    return stripe_subscription;
  }

  async findUserSubscriptionWithStripeSubscriptionId(
    stripe_subscription_id: string,
  ) {
    return await this.prisma.subscription_user.findFirst({
      where: {
        stripe_subscription_id,
      },
      include: {
        user: {
          select: {
            email: true,
            name: true,
          },
        },
        subscription: {
          select: {
            name: true,
          },
        },
      },
    });
  }

  private mapStripeStatusToUserStatus(
    stripeStatus: string,
  ): subscriptionUserStatus {
    switch (stripeStatus) {
      case 'active':
      case 'trialing':
        return 'active';
      case 'cancelled':
        return 'cancelled';
      case 'canceled':
        return 'cancelled';
      default:
        return 'invited';
    }
  }
}
