import {
  <PERSON>,
  <PERSON>,
  Headers,
  RawBodyRequest,
  Req,
  BadRequestException,
  Get,
  Res,
  Body,
} from '@nestjs/common';
import { StripeService } from './stripe.service';
import { Request } from 'express';

@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Post('webhook')
  async handleWebhook(
    @Req() req: any,
    @Res() res: Response,
    @Body() payload: any,
  ) {
    const sig = req.headers['stripe-signature'] as string;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    const event = payload;
    // console.log(request.rawBody)
    try {
      return await this.stripeService.handleWebhookEvent(event, sig);
    } catch (err) {
      console.error(err.message);
      throw new BadRequestException(`Webhook Error: ${err.message}`);
    }
  }
}
