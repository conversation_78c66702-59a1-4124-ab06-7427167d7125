import { Modu<PERSON> } from '@nestjs/common';
import { StripeService } from './stripe.service';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../prisma/prisma.module';
import { StripeController } from './stripe.controller';
import { EngagespotModule } from 'src/engagespot/engagespot.module';
import { CommonModule } from '../common/common.module';
import { StripeNotificationService } from './services/stripe-notifications.service';
import { StripeUtilsService } from './services/stripe-utils.service';

@Module({
  imports: [ConfigModule, PrismaModule, CommonModule, EngagespotModule],
  providers: [StripeService, StripeNotificationService, StripeUtilsService],
  controllers: [StripeController],
  exports: [StripeService, StripeNotificationService, StripeUtilsService],
})
export class StripeModule {}
