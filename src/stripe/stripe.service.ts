import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { PrismaService } from '../prisma/prisma.service';
import { EncryptionService } from '../common/services/encryption.service';
import {
  subscriptionUserStatus,
  stripe_subscription_status,
  kyc_status,
  user_kyc,
  user,
} from '@prisma/client';
import { StripeNotificationService } from './services/stripe-notifications.service';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { WorkflowIdentifiers } from 'src/engagespot/constants/workflow.constants';
import { UserKyc } from 'src/user_kyc/entities/user_kyc.entity';
import { StripeUtilsService } from './services/stripe-utils.service';
@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private readonly engagespotService: EngagespotService,
    private readonly encryptionService: EncryptionService,
    private readonly stripeUtilsService: StripeUtilsService,
  ) {
    // Initialize Stripe with secret key from environment variables
    const stripeSecret = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!stripeSecret) {
      throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
    }
    this.stripe = new Stripe(stripeSecret, {
      apiVersion: '2025-05-28.basil',
    });
  }

  /**
   * Create a Stripe Billing Portal session for a customer.
   */
  async createPortalSession(customerId: string, returnUrl: string) {
    return this.stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });
  }

  /**
   * Create a Stripe customer.
   */
  async createCustomer(email: string, name?: string) {
    return this.stripe.customers.create({
      email,
      name,
    });
  }

  /**
   * Create a Stripe product.
   */
  async createProduct(name: string, description?: string) {
    return this.stripe.products.create({
      name,
      description: description && description !== '' ? description : undefined,
    });
  }

  /**
   * Create a Stripe price for a product.
   */
  async createPrice(productId: string, unitAmount: number) {
    return this.stripe.prices.create({
      product: productId,
      unit_amount: Math.round(unitAmount * 100), // Stripe expects amount in cents
      currency: 'cad',
      recurring: {
        interval: 'month',
      },
    });
  }

  /**
   * Create a Stripe Checkout session for a subscription.
   * Also creates a transaction record in the database.
   */
  async createSubscriptionCheckoutSession(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string,
    subscriptionId: number,
    subscriptionUserId: number,
    amount: number,
    userId: number,
  ) {
    // Get subscription details with owner
    const subscription = await this.prisma.subscription.findUnique({
      where: { id: subscriptionId },
      include: {
        owner: {
          select: {
            stripe_connect_account_id: true,
          },
        },
      },
    });

    if (!subscription?.owner?.stripe_connect_account_id) {
      throw new Error("Subscription owner's Stripe Connect account not found");
    }
    try {
      const session = await this.stripe.checkout.sessions.create({
        customer: customerId,
        line_items: [
          {
            price: priceId,
          quantity: 1,
        },
      ],
      subscription_data: {
        transfer_data: {
          destination: subscription.owner.stripe_connect_account_id,
        },
      },
      mode: 'subscription',
      success_url: "https://app.feemates.com/profile/edit",
      cancel_url: "https://app.feemates.com/profile/edit",
      currency: 'cad',
    });

    // Record the transaction in the database
    await this.prisma.transactions.create({
      data: {
        user_id: userId,
        subscription_id: subscriptionId,
        subscription_user_id: subscriptionUserId,
        amount: amount,
        status: 'started',
        session_id: session.id,
      },
    });

    return session;
  } catch (error) {
    console.error('Error in createSubscriptionCheckoutSession: ', error);
    throw new BadRequestException('Failed to create stripe session');
  }

}

  /**
   * Get or create a Stripe customer for a user.
   */
  async getOrCreateCustomer(userId: number, email: string, name: string) {
    // Check if user already has a Stripe customer ID
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { stripe_customer_id: true },
    });

    if (user?.stripe_customer_id) {
      return user.stripe_customer_id;
    }

    // Create new customer in Stripe
    const customer = await this.createCustomer(email, name);

    // Save customer ID in the database
    await this.prisma.user.update({
      where: { id: userId },
      data: { stripe_customer_id: customer.id },
    });

    return customer.id;
  }

  /**
   * Handle Stripe webhook events.
   */
  async handleWebhookEvent(payload: any, sig: string) {
    const webhookSecret = this.configService.get<string>(
      'STRIPE_WEBHOOK_SECRET',
    );
    if (!webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET is not defined');
    }

    let event;
    try {
      // Uncomment below for real signature verification
      // event = this.stripe.webhooks.constructEvent(payload, sig, webhookSecret);
      event = payload;
    } catch (err) {
      throw new Error(`Webhook Error: ${err.message}`);
    }

    // Handle different event types
    switch (event.type) {
      case 'checkout.session.completed':
        await this.stripeUtilsService.handleCheckoutSessionCompleted(
          event.data.object,
        );
        break;
      case 'customer.subscription.updated':
        await this.stripeUtilsService.handleSubscriptionUpdated(
          event.data.object,
        );
        break;
      case 'customer.subscription.deleted':
        await this.stripeUtilsService.handleSubscriptionDeleted(
          event.data.object,
        );
        break;
      case 'invoice.paid':
        await this.stripeUtilsService.handleInvoicePaid(event.data.object);
        break;
      case 'invoice.payment_failed':
        await this.stripeUtilsService.handleInvoiceFailed(event.data.object);
        break;
      case 'payout.created':
        await this.stripeUtilsService.handlePayoutCreated(event.data.object);
        break;
      case 'payout.paid':
        await this.stripeUtilsService.handlePayoutPaid(event.data.object);
        break;
      case 'payout.failed':
        await this.stripeUtilsService.handlePayoutFailed(event.data.object);
        break;
    }

    return { received: true };
  }

  /**
   * Cancel a Stripe subscription.
   */
  async cancelSubscription(subscriptionId: string) {
    try {
      return await this.stripe.subscriptions.cancel(subscriptionId);
    } catch (error) {
      throw new Error(`Failed to cancel Stripe subscription: ${error.message}`);
    }
  }

  /**
   * Retrieve a subscription from Stripe.
   */
  async getSubscriptionFromStripe(subscription_id: string) {
    return await this.stripe.subscriptions.retrieve(subscription_id);
  }

  /**
   * Create a Stripe Connect account for a user.
   */
  async createConnectAccount(user: {
    id: number;
    stripe_connect_account_id?: string;
    email: string;
  }) {
    if (!user) {
      throw new Error('User not found');
    }

    if (user.stripe_connect_account_id) {
      return user.stripe_connect_account_id;
    }

    // Create Stripe Connect account
    const account = await this.stripe.accounts.create({
      type: 'custom',
      country: 'CA',
      email: user.email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
    });

    // Save Connect account ID in the database
    await this.prisma.user.update({
      where: { id: user.id },
      data: { stripe_connect_account_id: account.id },
    });

    // Update payout schedule for the account
    await this.updateAccountScheduleForPayout(account.id);

    return account.id;
  }

  /**
   * Generate an onboarding link for a Stripe Connect account.
   */
  async generateOnboardLink(account_id: string, return_url: string) {
    return await this.stripe.accountLinks.create({
      account: account_id,
      refresh_url: return_url,
      return_url: return_url,
      type: 'account_onboarding',
    });
  }

  async generateUpdateLink(account_id: string, return_url: string) {
    return await this.stripe.accountLinks.create({
      account: account_id,
      refresh_url: return_url,
      return_url: return_url,
      type: 'account_update',
    });
  }

  /**
   * Create an external bank account for a Stripe Connect account.
   */
  async createExternalBankAccount(kyc: user_kyc, account_id: string) {
    try {
      if (!kyc.account_number || !kyc.account_number_iv) {
        throw new BadRequestException('Invalid bank account details');
      }

      const accountNumber = this.encryptionService.decrypt(
        kyc.account_number,
        kyc.account_number_iv,
      );

      if (!kyc.transit_number || !kyc.institution_number) {
        throw new BadRequestException('Missing routing information');
      }

      // Create external bank account in Stripe
      const bankAccount = await this.stripe.accounts.createExternalAccount(
        account_id,
        {
          external_account: {
            object: 'bank_account',
            country: 'CA',
            currency: 'cad',
            account_holder_name: kyc.account_holder_name || '',
            account_holder_type: 'individual',
            routing_number: `${kyc.transit_number}${kyc.institution_number}`,
            account_number: accountNumber,
          },
        },
      );
      return bankAccount.id;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error(`Failed to create bank account: ${error.message}`);
    }
  }

  /**
   * Update payout schedule for a Stripe Connect account.
   */
  async updateAccountScheduleForPayout(ownerStripeAccountId: string) {
    return await this.stripe.accounts.update(ownerStripeAccountId, {
      settings: {
        payouts: { schedule: { interval: 'monthly', monthly_anchor: 1 } },
      },
    });
  }

  /**
   * Update a Stripe product.
   */
  async updateProduct(productId: string, name: string, description?: string) {
    const result = await this.stripe.products.update(productId, {
      name,
      description:
        description && description.trim() !== '' ? description : null,
    });
    console.log(result);
  }

  async retrieveStripeAccount(ownerStripeAccountId: string) {
    return await this.stripe.accounts.retrieve(ownerStripeAccountId);
  }

  async isStripeAccountOnBoardCompleted(
    ownerStripeAccountId: string,
  ): Promise<boolean> {
    const acct = await this.retrieveStripeAccount(ownerStripeAccountId);
    if (acct?.details_submitted) {
      return true;
    } else {
      return false;
    }
  }
}
