import {
  IsString,
  IsOptional,
  <PERSON>NotEmpty,
  Matches,
  Equals,
  MaxLength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserKycDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(160)
  account_holder_name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(160)
  bank_name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  // @Matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, {
  //   message: 'Invalid IFSC code format',
  // })
  ifsc_code?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  account_number: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  confirm_account_number: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  note?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  @MaxLength(160)
  transit_number?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  @MaxLength(160)
  institution_number?: string;

  @ApiProperty({
    description: 'Success URL for Stripe checkout',
    example: 'https://example.com/success',
  })
  @IsNotEmpty()
  // @IsUrl()
  return_url: string;
}
