import {
  IsString,
  IsOptional,
  IsNotEmpty,
  Matches,
  Equals,
  MaxLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserKycDto {
  @ApiProperty({
    description: 'Account holder name',
    example: '<PERSON>',
    maxLength: 160,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(160)
  account_holder_name: string;

  @ApiProperty({
    description: 'Bank name',
    example: 'State Bank of India',
    maxLength: 160,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(160)
  bank_name: string;

  @ApiPropertyOptional({
    description: 'IFSC code (Indian Financial System Code)',
    example: 'SBIN0001234',
  })
  @IsString()
  @IsOptional()
  // @Matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, {
  //   message: 'Invalid IFSC code format',
  // })
  ifsc_code?: string;

  @ApiProperty({
    description: 'Bank account number',
    example: '****************',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  account_number: string;

  @ApiProperty({
    description: 'Confirm bank account number (must match account_number)',
    example: '****************',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  confirm_account_number: string;

  @ApiPropertyOptional({
    description: 'Additional notes for KYC',
    example: 'Primary savings account',
  })
  @IsString()
  @IsOptional()
  note?: string;

  @ApiPropertyOptional({
    description: 'Transit number (for Canadian banks)',
    example: '12345',
    maxLength: 160,
  })
  @IsString()
  @IsOptional()
  @MaxLength(160)
  transit_number?: string;

  @ApiPropertyOptional({
    description: 'Institution number (for Canadian banks)',
    example: '001',
    maxLength: 160,
  })
  @IsString()
  @IsOptional()
  @MaxLength(160)
  institution_number?: string;

  @ApiProperty({
    description: 'Return URL after Stripe onboarding completion',
    example: 'https://example.com/success',
  })
  @IsNotEmpty()
  // @IsUrl()
  return_url: string;
}
