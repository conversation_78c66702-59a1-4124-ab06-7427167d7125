import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UserBasicInfoDto {
  @ApiProperty({
    example: 1,
    description: 'User identifier',
  })
  id: number;

  @ApiProperty({
    example: '<PERSON>',
    description: 'User full name',
  })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  email: string;
}

export class UserKycDataDto {
  @ApiProperty({
    example: 1,
    description: 'KYC record identifier',
  })
  id: number;

  @ApiProperty({
    example: 1,
    description: 'User identifier',
  })
  user_id: number;

  @ApiPropertyOptional({
    example: '<PERSON>',
    description: 'Account holder name',
  })
  account_holder_name?: string;

  @ApiProperty({
    example: 'State Bank of India',
    description: 'Bank name',
  })
  bank_name: string;

  @ApiPropertyOptional({
    example: 'SBIN0001234',
    description: 'IFSC code (decrypted)',
  })
  ifsc_code?: string;

  @ApiProperty({
    example: '****************',
    description: 'Account number (decrypted)',
  })
  account_number: string;

  @ApiProperty({
    example: 'pending',
    enum: ['pending', 'approved', 'rejected'],
    description: 'KYC status',
  })
  status: string;

  @ApiPropertyOptional({
    example: '12345',
    description: 'Transit number (for Canadian banks)',
  })
  transit_number?: string;

  @ApiPropertyOptional({
    example: '001',
    description: 'Institution number (for Canadian banks)',
  })
  institution_number?: string;

  @ApiPropertyOptional({
    example: 'ba_1234567890',
    description: 'Stripe bank account ID',
  })
  stripe_bank_account_id?: string;

  @ApiPropertyOptional({
    example: 'Additional notes',
    description: 'Additional notes for KYC',
  })
  note?: string;

  @ApiProperty({
    example: false,
    description: 'Whether Stripe onboarding is completed',
  })
  is_onboarding_completed: boolean;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'KYC creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'KYC last update timestamp',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    type: UserBasicInfoDto,
    description: 'User information (only in admin endpoints)',
  })
  user?: UserBasicInfoDto;
}

export class UserKycResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'User KYC retrieved successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    type: UserKycDataDto,
    description: 'KYC data',
  })
  data: UserKycDataDto;
}

export class UserKycListResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'KYC records retrieved successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    type: [UserKycDataDto],
    description: 'Array of KYC records',
  })
  data: UserKycDataDto[];
}

export class UserKycCreateUpdateResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'User KYC updated',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    example: {
      redirect: null,
      return_url: 'https://example.com/success',
    },
    description: 'Response data with redirect URL and return URL',
  })
  data: {
    redirect: string | null;
    return_url: string;
  };
}

export class UserKycMyKycResponseDto {
  @ApiProperty({
    example: 'User KYC',
    description: 'Response message',
  })
  message: string;

  @ApiProperty({
    example: true,
    description: 'Whether user KYC exists',
  })
  is_user_kyc_exists: boolean;

  @ApiProperty({
    type: UserKycDataDto,
    description: 'KYC data with decrypted fields',
  })
  data: UserKycDataDto;
}

export class UserKycApproveRejectResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'User KYC approved',
    description: 'Success message',
  })
  message: string;
}

export class OnboardLinkRequestDto {
  @ApiProperty({
    description: 'Return URL after Stripe onboarding',
    example: 'https://example.com/success',
  })
  return_url: string;
}

export class OnboardLinkResponseDto {
  @ApiProperty({
    example: true,
    description: 'Success status',
  })
  status: boolean;

  @ApiProperty({
    example: 'User KYC onboard link',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    example: {
      redirect: 'https://connect.stripe.com/setup/...',
      return_url: 'https://example.com/success',
    },
    description: 'Response data with Stripe onboard link and return URL',
  })
  data: {
    redirect: string;
    return_url: string;
  };
}
