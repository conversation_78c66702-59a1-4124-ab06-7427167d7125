import { Module } from '@nestjs/common';
import { UserKycService } from './user_kyc.service';
import { UserKycController } from './user_kyc.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { CommonModule } from '../common/common.module';
import { StripeModule } from '../stripe/stripe.module';

@Module({
  imports: [PrismaModule, CommonModule, StripeModule],
  controllers: [UserKycController],
  providers: [UserKycService],
  exports: [UserKycService],
})
export class UserKycModule {}
