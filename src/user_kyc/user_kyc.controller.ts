import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  ForbiddenException,
  Patch,
  BadRequestException,
} from '@nestjs/common';
import { UserKycService } from './user_kyc.service';
import { CreateUserKycDto } from './dto/create-user_kyc.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('User KYC')
@Controller('user-kyc')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserKycController {
  constructor(private readonly userKycService: UserKycService) {}

  @Get()
  @ApiOperation({ summary: 'Get all KYC records (Admin only)' })
  async findAll(@Request() req) {
    // TODO: Uncomment after adding role guard
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can view all KYCs');
    // }
    return this.userKycService.findAll();
  }

  @Get('my-kyc')
  @ApiOperation({ summary: "Get authenticated user's KYC details" })
  findMyKyc(@Request() req) {
    return this.userKycService.findUserKyc(req.user.id);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get KYC record by ID with decrypted values (Admin only)',
  })
  async findOne(@Request() req, @Param('id') id: string) {
    // TODO: Uncomment after adding role guard
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can view KYC details');
    // }
    return this.userKycService.findOne(+id);
  }

  @Post()
  @ApiOperation({ summary: 'Create or update KYC for authenticated user' })
  createOrUpdate(@Request() req, @Body() createUserKycDto: CreateUserKycDto) {
    return this.userKycService.createOrUpdate(req.user.id, createUserKycDto);
  }

  @Post('stripe/generate-onboard-url')
  @ApiOperation({ summary: 'Get Onboard Link' })
  getOnboardLink(@Request() req, @Body() data: any) {
    if (!data.return_url) {
      throw new BadRequestException('return_url required');
    }
    return this.userKycService.getOnboardLink(req.user.id, data);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'Approve KYC' })
  async approveKyc(@Request() req, @Param('id') id: string) {
    // TODO: Add role check for admin
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can approve KYC');
    // }
    return this.userKycService.approveKyc(+id);
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Reject KYC' })
  async rejectKyc(@Request() req, @Param('id') id: string) {
    // TODO: Add role check for admin
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can reject KYC');
    // }
    return this.userKycService.rejectKyc(+id);
  }
}
