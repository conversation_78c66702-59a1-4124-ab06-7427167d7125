import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  ForbiddenException,
  Patch,
  BadRequestException,
} from '@nestjs/common';
import { UserKycService } from './user_kyc.service';
import { CreateUserKycDto } from './dto/create-user_kyc.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import {
  UserKycListResponseDto,
  UserKycResponseDto,
  UserKycMyKycResponseDto,
  UserKycCreateUpdateResponseDto,
  UserKycApproveRejectResponseDto,
  OnboardLinkRequestDto,
  OnboardLinkResponseDto,
} from './dto/user-kyc-response.dto';

@ApiTags('User KYC')
@Controller('user-kyc')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UserKycController {
  constructor(private readonly userKycService: UserKycService) {}

  @Get()
  @ApiOperation({ summary: 'Get all KYC records (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'KYC records retrieved successfully',
    type: UserKycListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Only admins can view all KYCs' },
        error: { type: 'string', example: 'Forbidden' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async findAll(@Request() req) {
    // TODO: Uncomment after adding role guard
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can view all KYCs');
    // }
    return this.userKycService.findAll();
  }

  @Get('my-kyc')
  @ApiOperation({ summary: "Get authenticated user's KYC details" })
  @ApiResponse({
    status: 200,
    description: 'User KYC details retrieved successfully',
    type: UserKycMyKycResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  findMyKyc(@Request() req) {
    return this.userKycService.findUserKyc(req.user.id);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get KYC record by ID with decrypted values (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'KYC record retrieved successfully',
    type: UserKycResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC record not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'KYC not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: {
          type: 'string',
          example: 'Only admins can view KYC details',
        },
        error: { type: 'string', example: 'Forbidden' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async findOne(@Request() req, @Param('id') id: string) {
    // TODO: Uncomment after adding role guard
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can view KYC details');
    // }
    return this.userKycService.findOne(+id);
  }

  @Post()
  @ApiOperation({ summary: 'Create or update KYC for authenticated user' })
  @ApiResponse({
    status: 201,
    description: 'KYC created or updated successfully',
    type: UserKycCreateUpdateResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input or cannot modify approved KYC',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Cannot modify approved KYC' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'user not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  createOrUpdate(@Request() req, @Body() createUserKycDto: CreateUserKycDto) {
    return this.userKycService.createOrUpdate(req.user.id, createUserKycDto);
  }

  @Post('stripe/generate-onboard-url')
  @ApiOperation({ summary: 'Get Stripe onboard link for KYC completion' })
  @ApiResponse({
    status: 201,
    description: 'Onboard link generated successfully',
    type: OnboardLinkResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - return_url is required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'return_url required' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'User details not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  getOnboardLink(@Request() req, @Body() data: OnboardLinkRequestDto) {
    if (!data.return_url) {
      throw new BadRequestException('return_url required');
    }
    return this.userKycService.getOnboardLink(req.user.id, data);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'Approve KYC (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'KYC approved successfully',
    type: UserKycApproveRejectResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC record not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'KYC not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad Request - Onboarding not completed or other validation error',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example: 'User KYC onboarding not completed',
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Only admins can approve KYC' },
        error: { type: 'string', example: 'Forbidden' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async approveKyc(@Request() req, @Param('id') id: string) {
    // TODO: Add role check for admin
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can approve KYC');
    // }
    return this.userKycService.approveKyc(+id);
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'Reject KYC (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'KYC rejected successfully',
    type: UserKycApproveRejectResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'KYC record not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'KYC not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Only admins can reject KYC' },
        error: { type: 'string', example: 'Forbidden' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async rejectKyc(@Request() req, @Param('id') id: string) {
    // TODO: Add role check for admin
    // if (!req.user.roles?.includes('admin')) {
    //   throw new ForbiddenException('Only admins can reject KYC');
    // }
    return this.userKycService.rejectKyc(+id);
  }
}
