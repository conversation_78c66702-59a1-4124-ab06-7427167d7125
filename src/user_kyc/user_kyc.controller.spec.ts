import { Test, TestingModule } from '@nestjs/testing';
import { UserKycController } from './user_kyc.controller';
import { UserKycService } from './user_kyc.service';

describe('UserKycController', () => {
  let controller: UserKycController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserKycController],
      providers: [UserKycService],
    }).compile();

    controller = module.get<UserKycController>(UserKycController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
