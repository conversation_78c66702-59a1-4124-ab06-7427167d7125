import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EncryptionService } from '../common/services/encryption.service';
import { CreateUserKycDto } from './dto/create-user_kyc.dto';
import { kyc_status } from '@prisma/client';
import { ResponseService } from 'src/common/services/response.service';
import { StripeService } from '../stripe/stripe.service';

@Injectable()
export class UserKycService {
  constructor(
    private prisma: PrismaService,
    private encryptionService: EncryptionService,
    private responseService: ResponseService,
    private stripeService: StripeService,
  ) {}

  async findAll() {
    const kycs = await this.prisma.user_kyc.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return this.responseService.successResponse(
      'User KYCs retrieved successfully',
      kycs,
    );
  }

  async findOne(id: number) {
    const kyc = await this.prisma.user_kyc.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!kyc) {
      throw new NotFoundException('KYC not found');
    }

    // Decrypt sensitive data
    const decryptedKyc = {
      ...kyc,
      account_number: this.decryptAccountNumber(kyc),
      ifsc_code: kyc.ifsc_code ? this.decryptIfscCode(kyc) : null,
    };

    return this.responseService.successResponse(
      'User KYC retrieved successfully',
      decryptedKyc,
    );
  }

  async createOrUpdate(userId: number, createUserKycDto: CreateUserKycDto) {
    // Check if user has any KYC
    const existingKyc = await this.prisma.user_kyc.findFirst({
      where: {
        user_id: userId,
      },
    });

    const user = await this.prisma.user.findUnique({
      where: {
        id: userId,
      },
    });
    if (!user) {
      throw new NotFoundException('user not found');
    }
    if (existingKyc && existingKyc.status === kyc_status.approved) {
      throw new BadRequestException('Cannot modify approved KYC');
    }

    // Encrypt sensitive data
    const accountNumberEnc = this.encryptionService.encrypt(
      createUserKycDto.account_number,
    );
    const ifscCodeEnc = createUserKycDto.ifsc_code
      ? this.encryptionService.encrypt(createUserKycDto.ifsc_code)
      : null;

    const data = {
      user_id: userId,
      account_holder_name: createUserKycDto.account_holder_name,
      bank_name: createUserKycDto.bank_name,
      ifsc_code: ifscCodeEnc ? ifscCodeEnc.encryptedData : null,
      ifsc_code_iv: ifscCodeEnc ? ifscCodeEnc.iv : null,
      account_number: accountNumberEnc.encryptedData,
      account_number_iv: accountNumberEnc.iv,
      note: createUserKycDto.note,
      transit_number: createUserKycDto.transit_number,
      institution_number: createUserKycDto.institution_number,
      status: kyc_status.pending, // Reset to pending on update
    };

    // Create or update KYC record
    const kyc = existingKyc
      ? await this.prisma.user_kyc.update({
          where: { id: existingKyc.id },
          data,
        })
      : await this.prisma.user_kyc.create({
          data,
        });

    // Return decrypted data

    // const connectAccountId = await this.stripeService.createConnectAccount(
    //   user,
    // );
    // if (!kyc.stripe_bank_account_id) {
    //   const stripe_bank_account_id =
    //     await this.stripeService.createExternalBankAccount(
    //       kyc,
    //       connectAccountId,
    //     );

    //   await this.prisma.user_kyc.update({
    //     where: { id: kyc.id },
    //     data: {
    //       stripe_bank_account_id,
    //     },
    //   });
    // }
    // const redirect = await this.stripeService.generateOnboardLink(
    //   connectAccountId,
    //   createUserKycDto.return_url,
    // );

    return this.responseService.successResponse('User KYC updated', {
      redirect: null,
      return_url: createUserKycDto.return_url,
    });
  }

  async approveKyc(id: number) {
    // Fetch KYC with user data in a single query
    const kyc = await this.prisma.user_kyc.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            stripe_connect_account_id: true,
          },
        },
      },
    });

    if (!kyc) {
      throw new NotFoundException('KYC not found');
    }

    // Early return if no Stripe account exists
    if (!kyc.user?.stripe_connect_account_id) {
      return this.responseService.errorResponse(
        'No Stripe Connect account found for user',
      );
    }

    try {
      // Check if Stripe onboarding is completed
      const isOnboardingCompleted =
        await this.stripeService.isStripeAccountOnBoardCompleted(
          kyc.user.stripe_connect_account_id,
        );

      if (!isOnboardingCompleted) {
        return this.responseService.errorResponse(
          'User KYC onboarding not completed',
        );
      }

      // Use transaction to ensure data consistency
      await this.prisma.$transaction(async (tx) => {
        // Update KYC onboarding status
        await tx.user_kyc.update({
          where: { id: kyc.id },
          data: {
            is_onboarding_completed: true,
            status: kyc_status.approved,
          },
        });

        // Update user KYC verification status
        await tx.user.update({
          where: { id: kyc.user_id },
          data: { is_kyc_verified: true },
        });
      });

      // Update Stripe payout schedule (outside transaction as it's external service)
      await this.stripeService.updateAccountScheduleForPayout(
        kyc.user.stripe_connect_account_id,
      );

      return this.responseService.successResponse('User KYC approved');
    } catch (error) {
      // Revert KYC status on any failure
      await this.prisma.user_kyc.update({
        where: { id },
        data: {
          status: kyc_status.pending,
          is_onboarding_completed: false,
        },
      });

      throw new BadRequestException(`Failed to approve KYC: ${error.message}`);
    }
  }

  async rejectKyc(id: number) {
    const kyc = await this.prisma.user_kyc.findUnique({
      where: { id },
    });

    if (!kyc) {
      throw new NotFoundException('KYC not found');
    }

    // Update KYC status and user verification
    await this.prisma.$transaction([
      this.prisma.user_kyc.update({
        where: { id },
        data: { status: kyc_status.pending },
      }),
      this.prisma.user.update({
        where: { id: kyc.user_id },
        data: { is_kyc_verified: false },
      }),
    ]);
    return this.responseService.successResponse('User KYC updated');
  }

  async findUserKyc(userId: number) {
    const kyc = await this.prisma.user_kyc.findFirst({
      where: { user_id: userId },
      include: {
        user: {
          select: {
            id: true,
            stripe_connect_account_id: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Early return if no KYC found
    if (!kyc) {
      return {
        message: 'User KYC',
        is_user_kyc_exists: false,
        data: null,
      };
    }

    let isOnboardingCompleted = kyc.is_onboarding_completed;

    // Check and update Stripe onboarding status if account exists
    if (kyc.user?.stripe_connect_account_id) {
      try {
        const stripeOnboardingStatus =
          await this.stripeService.isStripeAccountOnBoardCompleted(
            kyc.user.stripe_connect_account_id,
          );

        // Only update if status has changed from false to true
        if (stripeOnboardingStatus && !kyc.is_onboarding_completed) {
          await this.prisma.user_kyc.update({
            where: { id: kyc.id },
            data: { is_onboarding_completed: true },
          });

          await this.stripeService.updateAccountScheduleForPayout(
            kyc.user.stripe_connect_account_id,
          );

          isOnboardingCompleted = true;
        } else {
          isOnboardingCompleted = stripeOnboardingStatus;
        }
      } catch (error) {
        // Log error but don't fail the entire operation
        console.error(
          'Failed to check Stripe onboarding status:',
          error.message,
        );
      }
    }

    // Prepare response data with decrypted fields
    const data = {
      ...kyc,
      account_number: this.decryptAccountNumber(kyc),
      ifsc_code: kyc.ifsc_code ? this.decryptIfscCode(kyc) : null,
      is_onboarding_completed: isOnboardingCompleted,
    };

    return {
      message: 'User KYC',
      is_user_kyc_exists: true,
      data,
    };
  }
  async findUserKycByUserId(userId: number) {
    const kyc = await this.prisma.user_kyc.findFirst({
      where: { user_id: userId },
      orderBy: { createdAt: 'desc' },
    });

    if (!kyc) return null;

    return {
      ...kyc,
      account_number: this.decryptAccountNumber(kyc),
      ifsc_code: kyc.ifsc_code ? this.decryptIfscCode(kyc) : null,
    };
  }

  private decryptAccountNumber(kyc: any): string {
    return this.encryptionService.decrypt(
      kyc.account_number,
      kyc.account_number_iv,
    );
  }

  private decryptIfscCode(kyc: any): string {
    return this.encryptionService.decrypt(kyc.ifsc_code, kyc.ifsc_code_iv);
  }

  async getOnboardLink(user_id: number, data: any) {
    const user = await this.prisma.user.findUnique({
      where: { id: user_id },
      select: { id: true, stripe_connect_account_id: true, email: true },
    });

    if (!user) {
      throw new NotFoundException('User details not found');
    }

    const connectAccountId = user.stripe_connect_account_id
      ? user.stripe_connect_account_id
      : await this.stripeService.createConnectAccount({
          id: user.id,
          email: user.email,
          stripe_connect_account_id: undefined,
        });

    const isKycVerified =
      await this.updateUserKycStatusAndReturnStatus(user_id);

    const redirect = isKycVerified
      ? await this.stripeService.generateUpdateLink(
          connectAccountId,
          data.return_url,
        )
      : await this.stripeService.generateOnboardLink(
          connectAccountId,
          data.return_url,
        );

    return this.responseService.successResponse('User KYC onboard link', {
      redirect,
      return_url: data.return_url,
    });
  }

  async updateUserKycStatusAndReturnStatus(user_id: number): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: user_id },
      select: { id: true, stripe_connect_account_id: true },
    });

    if (!user?.stripe_connect_account_id) return false;

    const isOnboardingCompleted =
      await this.stripeService.isStripeAccountOnBoardCompleted(
        user.stripe_connect_account_id,
      );

    if (isOnboardingCompleted) {
      await this.prisma.user.update({
        where: { id: user_id },
        data: { is_kyc_verified: true },
      });
    }

    return isOnboardingCompleted;
  }
}
