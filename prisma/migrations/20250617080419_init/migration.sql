-- CreateTable
CREATE TABLE `User` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VA<PERSON><PERSON><PERSON>(191) NULL,
    `email` VARCHAR(191) NOT NULL,
    `password` VARCHAR(191) NULL,
    `mobile` VA<PERSON>HAR(191) NULL,
    `is_kyc_verified` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    `avatar` VARCHAR(191) NULL,
    `google_id` VARCHAR(191) NULL,
    `email_verified_at` DATETIME(3) NULL,
    `status` ENUM('active', 'disabled') NOT NULL DEFAULT 'active',
    `deletedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `User_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
