model user_kyc {
  id                      Int        @id @default(autoincrement())
  user_id                 Int
  account_holder_name     String?
  bank_name               String
  ifsc_code               String?
  ifsc_code_iv            String? // IV for ifsc_code encryption
  account_number          String
  account_number_iv       String // IV for account_number encryption
  status                  kyc_status @default(pending)
  transit_number          String?
  institution_number      String?
  stripe_bank_account_id  String?
  note                    String?
  is_onboarding_completed Boolean    @default(false)
  createdAt               DateTime   @default(now())
  updatedAt               DateTime   @updatedAt
  user                    user       @relation(fields: [user_id], references: [id])

  @@map("user_kyc")
}

enum kyc_status {
  pending
  approved
  rejected
}
