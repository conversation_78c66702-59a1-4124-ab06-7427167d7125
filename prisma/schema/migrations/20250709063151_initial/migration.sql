-- CreateTable
CREATE TABLE `payouts` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `amount` INTEGER NOT NULL,
    `currency` VARCHAR(191) NOT NULL,
    `destination` VARCHAR(191) NOT NULL,
    `status` ENUM('pending', 'in_transit', 'paid', 'failed', 'canceled') NOT NULL DEFAULT 'pending',
    `arrival_date` DATETIME(3) NOT NULL,
    `balance_transaction_id` VARCHAR(191) NOT NULL,
    `statement_descriptor` VARCHAR(191) NULL,
    `type` VARCHAR(191) NOT NULL,
    `failure_code` VARCHAR(191) NULL,
    `failure_message` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `metadata` JSON NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `roles` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `subscriptions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `thumbnail` VARCHAR(191) NULL,
    `status` ENUM('active', 'disabled', 'expired') NOT NULL DEFAULT 'active',
    `price` DOUBLE NULL,
    `per_person_price` DOUBLE NULL,
    `owner_share` DOUBLE NULL,
    `max_no_of_participants` INTEGER NOT NULL,
    `members_count` INTEGER NOT NULL,
    `created_by` INTEGER NOT NULL,
    `startDate` DATE NOT NULL,
    `endDate` DATE NOT NULL,
    `stripe_plan_id` VARCHAR(191) NULL,
    `stripe_plan__price_id` VARCHAR(191) NULL,
    `deletedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `subscription_template` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `thumbnail` VARCHAR(191) NULL,
    `price` DOUBLE NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `subscription_users` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `subscription_id` INTEGER NOT NULL,
    `invited_email` VARCHAR(191) NULL,
    `status` ENUM('invited', 'cancelled', 'declined', 'active', 'expired', 'removed') NOT NULL DEFAULT 'invited',
    `user_type` ENUM('owner', 'member') NOT NULL DEFAULT 'member',
    `price` DOUBLE NOT NULL,
    `joined_at` DATETIME(3) NULL,
    `removed_by` INTEGER NULL,
    `stripe_subscription_id` VARCHAR(191) NULL,
    `start_date` DATETIME(3) NULL,
    `end_date` DATETIME(3) NULL,
    `next_due_date` DATETIME(3) NULL,
    `cancelled_date` DATETIME(3) NULL,
    `cancellation_effective_date` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `stripe_subscription_status` ENUM('created', 'authenticated', 'active', 'pending', 'halted', 'cancelled', 'completed', 'expired', 'past_due', 'unpaid', 'canceled', 'incomplete', 'incomplete_expired', 'trialing', 'paused') NOT NULL DEFAULT 'created',

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `transactions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `subscription_id` INTEGER NOT NULL,
    `subscription_user_id` INTEGER NULL,
    `payment_date` DATE NULL,
    `amount` DOUBLE NOT NULL,
    `status` ENUM('started', 'completed', 'failed', 'pending') NOT NULL DEFAULT 'started',
    `session_id` VARCHAR(191) NULL,
    `deletedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_kyc` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `account_holder_name` VARCHAR(191) NULL,
    `bank_name` VARCHAR(191) NOT NULL,
    `ifsc_code` VARCHAR(191) NULL,
    `ifsc_code_iv` VARCHAR(191) NULL,
    `account_number` VARCHAR(191) NOT NULL,
    `account_number_iv` VARCHAR(191) NOT NULL,
    `status` ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    `transit_number` VARCHAR(191) NULL,
    `institution_number` VARCHAR(191) NULL,
    `stripe_bank_account_id` VARCHAR(191) NULL,
    `note` VARCHAR(191) NULL,
    `is_onboarding_completed` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_roles` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `role_id` INTEGER NOT NULL,

    UNIQUE INDEX `user_roles_user_id_role_id_key`(`user_id`, `role_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `users` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NULL,
    `email` VARCHAR(191) NOT NULL,
    `password` VARCHAR(191) NULL,
    `mobile` VARCHAR(191) NULL,
    `is_kyc_verified` BOOLEAN NOT NULL DEFAULT false,
    `avatar` VARCHAR(191) NULL,
    `google_id` VARCHAR(191) NULL,
    `email_verified_at` DATETIME(3) NULL,
    `status` ENUM('active', 'disabled') NOT NULL DEFAULT 'active',
    `refresh_token` VARCHAR(191) NULL,
    `stripe_customer_id` VARCHAR(191) NULL,
    `stripe_connect_account_id` VARCHAR(191) NULL,
    `stripe_bank_account_id` VARCHAR(191) NULL,
    `remember_token` VARCHAR(191) NULL,
    `failed_login_attempts` INTEGER NOT NULL DEFAULT 0,
    `lock_until` DATETIME(3) NULL,
    `deletedAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `users_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `payouts` ADD CONSTRAINT `payouts_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subscriptions` ADD CONSTRAINT `subscriptions_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subscription_users` ADD CONSTRAINT `subscription_users_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subscription_users` ADD CONSTRAINT `subscription_users_subscription_id_fkey` FOREIGN KEY (`subscription_id`) REFERENCES `subscriptions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `transactions` ADD CONSTRAINT `transactions_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `transactions` ADD CONSTRAINT `transactions_subscription_user_id_fkey` FOREIGN KEY (`subscription_user_id`) REFERENCES `subscription_users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_kyc` ADD CONSTRAINT `user_kyc_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_roles` ADD CONSTRAINT `user_roles_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_roles` ADD CONSTRAINT `user_roles_role_id_fkey` FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
