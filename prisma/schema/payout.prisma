model Payout {
    id                     Int          @id @default(autoincrement())
    user_id                Int
    amount                 Int
    currency               String
    destination            String
    status                 PayoutStatus @default(pending)
    arrival_date           DateTime     @map("arrival_date")
    balance_transaction_id String       @map("balance_transaction_id")
    statement_descriptor   String?      @map("statement_descriptor")
    type                   String
    failure_code           String?      @map("failure_code")
    failure_message        String?      @map("failure_message")
    createdAt              DateTime     @default(now()) @map("created_at")
    metadata               Json?
    user                   user         @relation(fields: [user_id], references: [id], onDelete: Cascade)

    @@map("payouts")
}

enum PayoutStatus {
    pending
    in_transit
    paid
    failed
    canceled
}
