// this subscription table refer as plan and price in stripe
model subscription {
  id                     Int                @id @default(autoincrement())
  name                   String
  description            String?
  thumbnail              String?
  status                 SubscriptionStatus @default(active)
  price                  Float?
  per_person_price       Float?
  owner_share            Float?
  max_no_of_participants Int
  members_count          Int
  created_by             Int
  startDate              DateTime           @db.Date
  endDate                DateTime           @db.Date
  stripe_plan_id         String?
  stripe_plan__price_id  String?

  deletedAt DateTime?
  createdAt DateTime  @default(now())

  // Relations
  owner   user                @relation("UserOwnedSubscriptions", fields: [created_by], references: [id], onDelete: Cascade)
  members subscription_user[]

  @@map("subscriptions")
}

enum SubscriptionStatus {
  active
  disabled
  expired
  cancelled
}
