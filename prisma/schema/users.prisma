model user {
  id                        Int        @id @default(autoincrement())
  name                      String?
  email                     String     @unique
  password                  String?
  mobile                    String?
  is_kyc_verified           <PERSON><PERSON><PERSON>    @default(false)
  avatar                    String?
  google_id                 String?
  email_verified_at         DateTime?
  status                    UserStatus @default(active)
  refresh_token             String?
  stripe_customer_id        String?
  stripe_connect_account_id String?
  stripe_bank_account_id    String?
  remember_token            String?
  failed_login_attempts     Int        @default(0)
  otp_secret                String?
  lock_until                DateTime?
  deletedAt                 DateTime?
  createdAt                 DateTime   @default(now())

  //                 Relations
  userRoles          user_role[]
  ownerSubscriptions subscription[]      @relation("UserOwnedSubscriptions")
  subscriptionUsers  subscription_user[]
  userKyc            user_kyc[]
  transactions       transactions[]
  payouts            Payout[]

  @@map("users")
}

enum UserStatus {
  active
  disabled
}
