model subscription_user {
  id               Int                    @id @default(autoincrement())
  user_id          Int?
  subscription_id  Int
  invited_email    String?
  status           subscriptionUserStatus @default(invited)
  user_type        user_type              @default(member)
  //   payment_status              payment_status          @default(pending)
  price            Float
  joined_at        DateTime?
  removed_by       Int?
  removed_at       DateTime?
  token            String?
  token_expires_at DateTime?

  stripe_subscription_id      String?
  start_date                  DateTime?
  end_date                    DateTime?
  next_due_date               DateTime?
  cancelled_date              DateTime?
  cancellation_effective_date DateTime?
  createdAt                   DateTime                   @default(now())
  stripe_subscription_status  stripe_subscription_status @default(created)
  //                          Relations
  user                        user?                      @relation(fields: [user_id], references: [id])
  subscription                subscription               @relation(fields: [subscription_id], references: [id])
  transaction                 transactions[]

  @@map("subscription_users")
}

enum subscriptionUserStatus {
  invited
  cancelled
  declined
  active
  expired
  removed
  pending
}

enum user_type {
  owner
  member
}

// enum payment_status {
//     started
//     completed
//     failed
//     pending
// }
enum stripe_subscription_status {
  created
  authenticated
  active
  pending
  halted
  cancelled
  completed
  expired
  past_due
  unpaid
  canceled
  incomplete
  incomplete_expired
  trialing
  paused
}
