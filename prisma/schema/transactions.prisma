model transactions {
  id                    Int             @id @default(autoincrement())
  user_id               Int?
  subscription_id       Int
  subscription_user_id  Int?
  payment_date          DateTime?       @db.Date
  amount                Float
  status                payment_status  @default(started)
  session_id            String?
  deletedAt             DateTime?
  createdAt             DateTime        @default(now())

  // Relations
  user user? @relation(fields: [user_id], references: [id])
  member subscription_user? @relation(fields: [subscription_user_id], references: [id])

  // (add other relations as needed)
}

enum payment_status {
  started
  completed
  failed
  pending
}