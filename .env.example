# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# The following `prisma+postgres` URL is similar to the URL produced by running a local Prisma Postgres 
# server with the `prisma dev` CLI command, when not choosing any non-default ports or settings. The API key, unlike the 
# one found in a remote Prisma Postgres URL, does not contain any sensitive information.

DATABASE_URL="mysql://root:password@127.0.0.1:3306/feematesdb"

AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=ap-south-1
AWS_PUBLIC_BUCKET_NAME=your_bucket_name

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_API_KEY='sk_test_your_stripe_api'
STRIPE_WEBHOOK_SECRET="sk_test_your_stripe_webhook"

GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/login
FRONTEND_URL=http://localhost:4200


# Login Security Configuration
# Maximum number of failed login attempts before account is locked (default: 5)
MAX_LOGIN_ATTEMPTS=5
# Duration in minutes for which account remains locked after exceeding max attempts (default: 15)
LOGIN_LOCK_DURATION=15

# JWT Configuration (optional)
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=30d

ENGAGESPOT_API_KEY="your_engagespot_api_key"
ENGAGESPOT_API_SECRET="your_engagespot_api_secret"