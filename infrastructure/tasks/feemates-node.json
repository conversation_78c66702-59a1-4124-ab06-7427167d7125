{"family": "feemates-node", "executionRoleArn": "arn:aws:iam::924279027850:role/feemates-node-task-exec-role", "networkMode": "bridge", "containerDefinitions": [{"name": "feemates-node", "image": "924279027850.dkr.ecr.ca-central-1.amazonaws.com/feemates/node", "cpu": 0, "memory": 3680, "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "essential": true, "environmentFiles": [{"value": "arn:aws:s3:::feemates-prod/production.env", "type": "s3"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/feemates-node", "awslogs-region": "ca-central-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}], "cpu": "2048", "memory": "3680", "requiresCompatibilities": ["EC2"], "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "volumes": []}